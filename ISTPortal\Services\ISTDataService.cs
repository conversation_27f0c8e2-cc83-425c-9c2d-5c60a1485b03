﻿using ISTPortal.Components.Pages;
using ISTPortal.Components.IST;
using ISTPortal.Data;
using ISTPortal.DTO;
using ISTPortal.Models;
using OfficeOpenXml;
using Microsoft.FluentUI.AspNetCore.Components.DesignTokens;
using Microsoft.EntityFrameworkCore;
using System.Net.NetworkInformation;
using System.Runtime.CompilerServices;
using Dapper;
using Microsoft.Data.SqlClient;
using System.Data;
using Syncfusion.Blazor.Schedule.Internal;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.RichTextEditor;

namespace ISTPortal.Services
{
    public class ISTDataService
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;
        public ISTDataService(ApplicationDbContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
            _connectionString = _configuration.GetConnectionString("DefaultConnection")!;
        }

        public async Task<List<ISTDTO>> getIST(string loginId, filterForm filters)
        {
            int userId = await getUserIdInt(loginId);

            var q = (from a in _context.Ists
                     join fromStores in _context.Stores on a.IstFromStoreId equals fromStores.Id
                     join toStores in _context.Stores on a.IstToStoreId equals toStores.Id
                     join uCreatedBy in _context.Users on a.IstCreatedBy equals uCreatedBy.Id
                     join uModifiedBy in _context.Users on a.IstModifiedBy equals uModifiedBy.Id
                     where a.IstIsActive == true
                     orderby a.IstCreatedDate descending
                     select new ISTDTO
                     {
                         IstId = a.IstId,
                         IstCode = a.IstCode,
                         IstTitle = a.IstTitle,
                         IstDescription = a.IstDescription,
                         IstFromStoreId = a.IstFromStoreId,
                         fromStore = fromStores.Name,
                         fromStoreCode = fromStores.Code,
                         IstToStoreId = a.IstToStoreId,
                         toStore = toStores.Name,
                         toStoreCode = toStores.Code,
                         TotalItems = _context.IstDetails.Count(x => x.IstId == a.IstId),
                         TotalQty = _context.IstDetails.Where(x => x.IstId == a.IstId).Sum(x => x.ItemQty),
                         ISTStatus = _context.IstProgresses.Where(x => x.IstId == a.IstId).OrderByDescending(x => x.IstProgressCreatedDate).Select(x => x.IstProgressName).FirstOrDefault(),
                         IstCreatedByString = uCreatedBy.UserId,
                         IstCreatedDate = a.IstCreatedDate,
                         IstModifiedByString = uModifiedBy.UserId,
                         IstModifiedDate = a.IstModifiedDate,
                         IstActive = ((bool)a.IstIsActive! ? "Yes" : "No"),
                         IstIsActive = a.IstIsActive,
                         istPriority = a.istPriority,
                         istInputMode = a.istInputMode == "Excel" ? 1 : 2,
                     }).ToList();


            return await Task.FromResult(q);
        }

        public async Task<int> getUserIdInt(string loginId)
        {
            return _context.Users.FirstOrDefault(x => x.UserId == loginId)!.Id;
        }

        public async Task<List<StoresDTO>> getUserStores(string loginId)
        {
            int userId = await getUserIdInt(loginId);
            var q = (from a in _context.Stores
                     join b in _context.UserStores on a.Id equals b.StoreId
                     where a.IsActive == true
                     orderby a.Name
                     select new StoresDTO
                     {
                         storeId = a.Id,
                         storeCode = a.Code,
                         StoreTitle = a.Name
                     }).ToList();
            return await Task.FromResult(q);
        }

        public async Task<int> getItemOnHandQtyAX(string itemCode, string itemMasterCode)
        {
            var pram = new { itemCode = itemCode, itemMasterCode = itemMasterCode };
            var con = _context.Database.GetDbConnection();
            var q =
               con.Query<int>("SP_GetItemOnHandQty", pram, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return await Task.FromResult(q);
        }

        public async Task<int> SaveIST(ISTDTO istMaster, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            var chkExists = _context.Ists.Any(x => x.IstId == istMaster.IstId);
            if (!chkExists)
            {
                var q = new Ist();
                q.IstCode = istMaster.IstCode;
                q.IstTitle = istMaster.IstTitle;
                q.IstDescription = istMaster.IstDescription;
                q.IstFromStoreId = _context.Stores.FirstOrDefault(x => x.Code == istMaster.fromStoreCode)!.Id;
                q.IstToStoreId = _context.Stores.FirstOrDefault(x => x.Code == istMaster.toStoreCode)!.Id;
                q.IstCreatedBy = userId;
                q.IstCreatedDate = DateTime.Now;
                q.IstModifiedBy = userId;
                q.IstModifiedDate = DateTime.Now;
                q.IstIsActive = true;
                q.istPriority = istMaster.istPriority;
                q.istInputMode = istMaster.istInputMode == 1 ? "Excel" : "Manual";

                _context.Ists.Add(q);
                _context.SaveChanges();
                istMaster.IstId = q.IstId;

                foreach (var item in istMaster.istDetailDtos)
                {
                    var qDet = new IstDetail();
                    qDet.IstId = istMaster.IstId;
                    qDet.ItemCode = item.ItemCode;
                    qDet.ItemMasterCode = item.ItemCode;
                    qDet.ItemQty = item.ItemQty;
                    qDet.istOnHandQty = await getItemOnHandQtyAX(item.ItemCode!, item.ItemMasterCode!);
                    qDet.IstDetailIsActive = true;
                    _context.IstDetails.Add(qDet);
                    _context.SaveChanges();
                }

                saveIstProgress(istMaster.IstId ?? 0, 1, "DRAFT", userId);
                return await Task.FromResult(istMaster.IstId ?? 0);

            }
            else
            {
                var q = _context.Ists.SingleOrDefault(x => x.IstId == istMaster.IstId);
                q.IstCode = istMaster.IstCode;
                q.IstTitle = istMaster.IstTitle;
                q.IstDescription = istMaster.IstDescription;
                q.IstFromStoreId = _context.Stores.FirstOrDefault(x => x.Code == istMaster.fromStoreCode)!.Id;
                q.IstToStoreId = _context.Stores.FirstOrDefault(x => x.Code == istMaster.toStoreCode)!.Id;
                q.istPriority = istMaster.istPriority;
                q.istInputMode = istMaster.istInputMode == 1 ? "Excel" : "Manual";
                q.IstModifiedBy = userId;
                q.IstModifiedDate = DateTime.Now;

                _context.SaveChanges();

                var itemsExist = _context.IstDetails.Where(x => x.IstId == istMaster.IstId).ToList();
                foreach (var item in itemsExist)
                {
                    _context.IstDetails.Remove(item);
                    _context.SaveChanges();
                }

                foreach (var item in istMaster.istDetailDtos)
                {
                    var qDet = new IstDetail();
                    qDet.IstId = istMaster.IstId;
                    qDet.ItemCode = item.ItemCode;
                    qDet.ItemMasterCode = item.ItemCode;
                    qDet.ItemQty = item.ItemQty;
                    qDet.IstDetailIsActive = true;
                    _context.IstDetails.Add(qDet);
                    _context.SaveChanges();
                }

                return await Task.FromResult(istMaster.IstId ?? 0);
            }
        }


        public async Task<string> checkTONoIsExist(string toNumber)
        {
            var qSB = _context.StoreBatches.Any(x => x.StoreBatchTonumber == toNumber);
            if (qSB) return await Task.FromResult("TO # already exist");

            var qSBCWH = _context.StoreBatchesCwhs.Any(x => x.CwhBatchTonumber == toNumber);
            if (qSB) return await Task.FromResult("TO # already exist");

            return await Task.FromResult("OK");
        }

        public async Task<List<ISTDetailDTO>> getISTItems(int? istId)
        {
            var itemsExist = (from x in _context.IstDetails
                              where x.IstId == istId
                              select new ISTDetailDTO
                              {
                                  IstId = x.IstId,
                                  ItemCode = x.ItemCode,
                                  ItemMasterCode = x.ItemMasterCode,
                                  ItemQty = x.ItemQty,
                                  IstDetailIsActive = x.IstDetailIsActive,
                              }).ToList();

            foreach (var item in itemsExist)
            {
                var wmsItem = await getWMSItemInfo(item.ItemCode!);
                item.ItemDesc = wmsItem != null ? wmsItem.ItemDesc : "";
                item.HIR1 = wmsItem != null ? wmsItem.HIR1 : "";
                item.HIR2 = wmsItem != null ? wmsItem.HIR2 : "";
                item.HIR3 = wmsItem != null ? wmsItem.HIR3 : "";
            }

            return await Task.FromResult(itemsExist);
        }

        public async Task<List<StoreBatchDetailDTO>> getStoreItems(int? storeBatchid)
        {
            var itemsExist = (from x in _context.StoreBatchDetails
                              where x.StoreBatchId == storeBatchid
                              select new StoreBatchDetailDTO
                              {
                                  StoreBatchId = storeBatchid,
                                  ItemCode = x.ItemCode,
                                  ItemMasterCode = x.ItemMasterCode,
                                  ItemQty = x.ItemQty,
                                  StoreBatchDetailIsActive = x.StoreBatchDetailIsActive,
                                  StoreBatchDetailRemarks = x.StoreBatchDetailRemarks
                              }).ToList();

            foreach (var item in itemsExist)
            {
                var wmsItem = await getWMSItemInfo(item.ItemCode!);
                item.ItemDesc = wmsItem != null ? wmsItem.ItemDesc : "";
                item.HIR1 = wmsItem != null ? wmsItem.HIR1 : "";
                item.HIR2 = wmsItem != null ? wmsItem.HIR2 : "";
                item.HIR3 = wmsItem != null ? wmsItem.HIR3 : "";
            }

            return await Task.FromResult(itemsExist);
        }

        public async Task<List<CWHBatchDetailDTO>> getCWHItems(int? cwhBatchid)
        {
            var itemsExist = (from x in _context.StoreBatchDetailsCwhs
                              where x.CwhBatchId == cwhBatchid
                              select new CWHBatchDetailDTO
                              {
                                  CWHBatchId = cwhBatchid,
                                  ItemCode = x.ItemCode,
                                  ItemMasterCode = x.ItemMasterCode,
                                  ItemQty = x.ItemQty,
                                  ItemQtyReadOnly = x.ItemQty,
                                  CWHBatchDetailIsActive = x.CwhBatchDetailIsActive,
                                  CWHBatchDetailRemarks = x.CwhBatchDetailRemarks
                              }).ToList();

            foreach (var item in itemsExist)
            {
                var wmsItem = await getWMSItemInfo(item.ItemCode!);
                item.ItemDesc = wmsItem != null ? wmsItem.ItemDesc : "";
                item.HIR1 = wmsItem != null ? wmsItem.HIR1 : "";
                item.HIR2 = wmsItem != null ? wmsItem.HIR2 : "";
                item.HIR3 = wmsItem != null ? wmsItem.HIR3 : "";
            }

            return await Task.FromResult(itemsExist);
        }

        public async Task<ItemDTO> getWMSItemInfo(string itemCode)
        {
            var pram = new { itemCode = itemCode };
            var con = _context.Database.GetDbConnection();

            ItemDTO iInquiry =
               con.Query<ItemDTO>("SP_getItemInfo", pram, commandType: CommandType.StoredProcedure).FirstOrDefault()!;
            return await Task.FromResult(iInquiry);
        }

        public async Task<List<ISTDetailDTO>> ValidateExcelFile(Stream fileStream)
        {
            var result = new List<ISTDetailDTO>();

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using var package = new ExcelPackage(fileStream);
            var worksheet = package.Workbook.Worksheets[0];

            int rowCount = worksheet.Dimension.Rows;

            for (int row = 2; row <= rowCount; row++)
            {
                string itemCode = worksheet.Cells[row, 1].Text;
                var wmsItem = await getWMSItemInfo(itemCode);
                var importDto = new ISTDetailDTO
                {
                    ItemCode = worksheet.Cells[row, 1].Text,
                    ItemMasterCode = worksheet.Cells[row, 1].Text,
                    ItemQty = Convert.ToInt32(worksheet.Cells[row, 2].Text),
                    ItemDesc = wmsItem != null ? wmsItem.ItemDesc : "",
                    HIR1 = wmsItem != null ? wmsItem.HIR1 : "",
                    HIR2 = wmsItem != null ? wmsItem.HIR2 : "",
                    HIR3 = wmsItem != null ? wmsItem.HIR3 : "",
                };

                result.Add(importDto);
            }



            return await Task.FromResult(result);
        }

        public async Task<bool> getISTStatus(int istId)
        {
            var istStatus = _context.IstProgresses.Where(x => x.IstId == istId).OrderByDescending(x => x.IstProgressCreatedDate).Select(x => x.IstProgressName).FirstOrDefault();
            if (istStatus == "CANCEL")
            {
                return await Task.FromResult(false);
            }
            return await Task.FromResult(true);
        }

        public async Task<string> deleteIST(int istId, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            var q = _context.Ists.SingleOrDefault(x => x.IstId == istId);
            q.IstIsActive = false;
            q.IstModifiedBy = userId;
            q.IstModifiedDate = DateTime.Now;
            _context.SaveChanges();
            return await Task.FromResult("OK");
        }
        public async Task<string> sendIST(int istId, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            var q = _context.Ists.SingleOrDefault(x => x.IstId == istId);
            q.IstModifiedBy = userId;
            q.IstModifiedDate = DateTime.Now;
            _context.SaveChanges();

            saveIstProgress(q.IstId, 2, "SENT", userId);
            saveIstProgress(q.IstId, 3, "ACCEPT", userId);

            return await Task.FromResult("OK");
        }
        public async Task<string> cancelIST(int istId, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            var q = _context.Ists.SingleOrDefault(x => x.IstId == istId);
            q.IstModifiedBy = userId;
            q.IstModifiedDate = DateTime.Now;
            _context.SaveChanges();

            saveIstProgress(q.IstId, 18, "CANCEL", userId);

            return await Task.FromResult("OK");
        }

        public async Task<List<ISTDTO>> getISTForSource(string loginId, filterForm filters)
        {
            int userId = await getUserIdInt(loginId);
            var q = (from a in _context.Ists
                     join fromStores in _context.Stores on a.IstFromStoreId equals fromStores.Id
                     join toStores in _context.Stores on a.IstToStoreId equals toStores.Id
                     join uCreatedBy in _context.Users on a.IstCreatedBy equals uCreatedBy.Id
                     join uModifiedBy in _context.Users on a.IstModifiedBy equals uModifiedBy.Id
                     where a.IstIsActive == true
                     orderby a.IstCreatedDate descending
                     select new ISTDTO
                     {
                         IstId = a.IstId,
                         IstCode = a.IstCode,
                         IstTitle = a.IstTitle,
                         IstDescription = a.IstDescription,
                         IstFromStoreId = a.IstFromStoreId,
                         fromStore = fromStores.Name,
                         fromStoreCode = fromStores.Code,
                         IstToStoreId = a.IstToStoreId,
                         toStore = toStores.Name,
                         toStoreCode = toStores.Code,
                         TotalItems = _context.IstDetails.Count(x => x.IstId == a.IstId),
                         TotalQty = _context.IstDetails.Where(x => x.IstId == a.IstId).Sum(x => x.ItemQty),
                         ISTStatusId = _context.IstProgresses.Where(x => x.IstId == a.IstId).OrderByDescending(x => x.IstProgressCreatedDate).Select(x => x.IstStepId).FirstOrDefault(),
                         ISTStatus = _context.IstProgresses.Where(x => x.IstId == a.IstId).OrderByDescending(x => x.IstProgressCreatedDate).Select(x => x.IstProgressName).FirstOrDefault(),
                         IstCreatedByString = uCreatedBy.UserId,
                         IstCreatedDate = a.IstCreatedDate,
                         IstModifiedByString = uModifiedBy.UserId,
                         IstModifiedDate = a.IstModifiedDate,
                         IstActive = ((bool)a.IstIsActive! ? "Yes" : "No"),
                         IstIsActive = a.IstIsActive,
                     }).ToList();

            foreach (var item in q.ToList().AsEnumerable().Reverse())
            {
                if (item.ISTStatus == "DRAFT")
                    q.Remove(item);
            }
            var validISTIds = q.Select(x => x.IstId).ToList();
            var qStoreBatch = _context.StoreBatches
                               .Where(x => validISTIds.Contains(x.IstId) && x.StoreBatchOwnerType == "SOURCE")
                               .OrderBy(x => x.StoreBatchCreatedDate)
                               .Select(x => new StoreBatchDTO
                               {
                                   StoreBatchId = x.StoreBatchId,
                                   IstId = x.IstId,
                                   StoreMasterBatchCode = x.StoreMasterBatchCode,
                                   StoreBatchCode = x.StoreBatchCode,
                                   StoreBatchTonumber = x.StoreBatchTonumber,
                                   StoreBatchOwnerType = x.StoreBatchOwnerType,
                                   StoreBatchOwnerCode = x.StoreBatchOwnerCode,
                                   StoreBatchIsActive = x.StoreBatchIsActive,
                                   StoreBatchCreatedByString = _context.Users.FirstOrDefault(a => a.Id == x.StoreBatchCreatedBy)!.UserId,
                                   StoreBatchCreatedDate = x.StoreBatchCreatedDate,
                                   StoreBatchModifiedByString = _context.Users.FirstOrDefault(a => a.Id == x.StoreBatchModifiedBy)!.UserId,
                                   StoreBatchModifiedDate = x.StoreBatchModifiedDate,
                                   storeTOStatus = x.StoreTostatus,
                                   TOCreatedDateAX = x.TOCreatedDateAX ?? DateTime.Now,
                                   TOShippedDateAX = x.TOShippedDateAX ?? DateTime.Now,
                                   TOReceivedDateAX = x.TOReceivedDateAX ?? DateTime.Now,
                                   TotalItems = _context.StoreBatchDetails.Count(a => a.StoreBatchId == x.StoreBatchId),
                                   TotalQty = _context.StoreBatchDetails.Where(a => a.StoreBatchId == x.StoreBatchId).Sum(a => a.ItemQty) ?? 0
                               }).ToList();

            foreach (var item in q)
            {
                var checkStoreBatchExist = qStoreBatch.Any(x => x.IstId == item.IstId);
                if (checkStoreBatchExist)
                {
                    item.istStoreBatch = qStoreBatch.Where(x => x.IstId == item.IstId).ToList();
                }
            }

            return await Task.FromResult(q);
        }
        public async Task<string> rejectISTSrc(int istId, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            var q = _context.Ists.SingleOrDefault(x => x.IstId == istId);
            q.IstModifiedBy = userId;
            q.IstModifiedDate = DateTime.Now;
            _context.SaveChanges();

            saveIstProgress(q.IstId, 4, "REJECTED", userId);

            return await Task.FromResult("OK");
        }
        public async Task<string> acceptISTSrc(int istId, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            var q = _context.Ists.SingleOrDefault(x => x.IstId == istId);
            q.IstModifiedBy = userId;
            q.IstModifiedDate = DateTime.Now;
            _context.SaveChanges();

            saveIstProgress(q.IstId, 3, "ACCEPT", userId);

            return await Task.FromResult("OK");
        }

        public async Task<string> acceptTOCWH(int storeBatchId, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            var q = _context.StoreBatches.FirstOrDefault(x => x.StoreBatchId == storeBatchId);
            q.StoreTostatus = "ACCEPTED BY CWH";
            q.StoreBatchModifiedBy = userId;
            q.StoreBatchModifiedDate = DateTime.Now;
            _context.SaveChanges();

            saveStoreProgress(storeBatchId, "SOURCE", "ACCEPTED BY CWH", userId);
            return await Task.FromResult("OK");
        }
        public async Task<string> rejectTOCWH(int storeBatchId, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            var q = _context.StoreBatches.FirstOrDefault(x => x.StoreBatchId == storeBatchId);
            q.StoreTostatus = "REJECTED BY CWH";
            q.StoreBatchModifiedBy = userId;
            q.StoreBatchModifiedDate = DateTime.Now;
            _context.SaveChanges();

            saveStoreProgress(storeBatchId, "SOURCE", "REJECTED BY CWH", userId);
            return await Task.FromResult("OK");
        }

        public void saveIstProgress(int istId, int stepId, string progressName, int userId, string progressRemarks = "")
        {
            var qProgress = new IstProgress()
            {
                IstId = istId,
                IstStepId = stepId,
                IstProgressName = progressName,
                IstProgressRemarks = progressRemarks,
                IstProgressCreatedBy = userId,
                IstProgressCreatedDate = DateTime.Now,
                IstProgressModifiedBy = userId,
                IstProgressModifiedDate = DateTime.Now,
                IstProgressIsActive = true,
            };
            _context.IstProgresses.Add(qProgress);
            _context.SaveChanges();
        }
        public void saveStoreProgress(int storeBatchId, string ownerType, string progressName, int userId)
        {
            var qProgress = new storeProgress()
            {
                storeBatchId = storeBatchId,
                storeOwnerType = ownerType,
                storeProgressName = progressName,
                storeProgressCreatedBy = userId,
                storeProgressCreatedDate = DateTime.Now,
                storeProgressModifiedBy = userId,
                storeProgressModifiedDate = DateTime.Now,
                storeProgressIsActive = true,
            };
            _context.storeProgresses.Add(qProgress);
            _context.SaveChanges();
        }

        public async Task<List<ISTDetailDTO>> fetchTOItems(string toNumbers, List<ISTDetailDTO> istDet)
        {
            await using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var parameters = new DynamicParameters();
            parameters.Add("@toNumber", toNumbers);

            var result = await connection.QueryAsync<TOItemsDTO>(
                "dbo.sp_getTOItems",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            if (!result.Any())
                throw new Exception("TO Number not found.");

            foreach (var item in istDet)
            {
                var checkItemExist = result.Any(x => x.ItemCode == item.ItemCode);
                if (checkItemExist)
                {
                    item.fetchQty = result.FirstOrDefault(x => x.ItemCode == item.ItemCode)!.Quantity;
                }
            }

            foreach (var item in result)
            {
                var checkItemNotExist = istDet.Any(x => x.ItemCode == item.ItemCode);
                if (!checkItemNotExist)
                {
                    var det = new ISTDetailDTO();// istDet.FirstOrDefault(x => x.ItemCode == item.ItemCode);
                    det.IstId = istDet.FirstOrDefault()!.IstId;
                    det.IstDetailIsActive = true;
                    det.ItemCode = item.ItemCode;
                    det.ItemMasterCode = item.ItemCode;
                    det.ItemQty = 0;
                    det.fetchQty = item.Quantity;
                    det.TORemarks = "Additional TO Items";
                    istDet.Add(det);
                    //istDet = new List<ISTDetailDTO>(istDet) { det };
                }
            }

            return await Task.FromResult(istDet);
        }
        public async Task<List<StoreBatchDetailDTO>> fetchTOItemsforCWH(string toNumbers, List<StoreBatchDetailDTO> storeBatchDet)
        {
            await using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var parameters = new DynamicParameters();
            parameters.Add("@toNumber", toNumbers);

            var result = await connection.QueryAsync<TOItemsDTO>(
                "dbo.sp_getTOItems",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            if (!result.Any())
                throw new Exception("TO Number not found.");

            foreach (var item in storeBatchDet)
            {
                var checkItemExist = result.Any(x => x.ItemCode == item.ItemCode);
                if (checkItemExist)
                {
                    item.fetchQty = result.FirstOrDefault(x => x.ItemCode == item.ItemCode)!.Quantity;
                }
            }

            foreach (var item in result)
            {
                var checkItemNotExist = storeBatchDet.Any(x => x.ItemCode == item.ItemCode);
                if (!checkItemNotExist)
                {
                    var det = new StoreBatchDetailDTO();
                    det.StoreBatchId = storeBatchDet.FirstOrDefault()!.StoreBatchId;
                    det.StoreBatchDetailIsActive = true;
                    det.ItemCode = item.ItemCode;
                    det.ItemMasterCode = item.ItemCode;
                    det.ItemQty = 0;
                    det.fetchQty = item.Quantity;
                    det.TORemarks = "Additional TO Items";
                    storeBatchDet.Add(det);
                }
            }

            return await Task.FromResult(storeBatchDet);
        }

        public async Task<TOMasterDTO> getTOMasterData(string toNumber)
        {
            await using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            var parameters = new DynamicParameters();
            parameters.Add("@toNumber", toNumber);
            var result = await connection.QueryAsync<TOMasterDTO>(
                "dbo.getTOMaster",
                parameters,
                commandType: CommandType.StoredProcedure
            );
            if (!result.Any())
                throw new Exception("TO Number not found.");
            return await Task.FromResult(result.FirstOrDefault()!);
        }

        public async Task<string> saveTOItems(ISTDTO istMaster, string ownerType, string toStatus, string loginId, string isFullyCloseIST)
        {
            int userId = await getUserIdInt(loginId);
            var qExist = _context.StoreBatches.Any(x => x.IstId == istMaster.IstId && x.StoreBatchTonumber == istMaster.TONumber);
            var batchMasterCode = "";
            var batchCode = "";
            DateTime createdAX = DateTime.Now;
            DateTime shippedAX = DateTime.Now;
            DateTime receivedAX = DateTime.Now;
            var TOData = await getTOMasterData(istMaster.TONumber!);
            if (TOData != null)
            {
                createdAX = TOData.storeTOCreatedDateAX ?? DateTime.Now;
                shippedAX = TOData.storeTOShippedDateAX ?? DateTime.Now;
                receivedAX = TOData.storeTOReceivedDateAX ?? DateTime.Now;
            }

            if (!qExist)
            {
                batchMasterCode = Guid.NewGuid().ToString();
                batchCode = batchMasterCode;
            }
            else
            {
                var qStoreBatch = _context.StoreBatches.FirstOrDefault(x => x.IstId == istMaster.IstId && x.StoreBatchTonumber == istMaster.TONumber && x.StoreBatchOwnerType == ownerType);
                batchMasterCode = qStoreBatch!.StoreMasterBatchCode;
                batchCode = Guid.NewGuid().ToString(); ;
            }

            var q = new StoreBatch();
            q.IstId = istMaster.IstId;
            q.StoreMasterBatchCode = batchMasterCode;
            q.StoreBatchCode = batchCode;
            q.StoreBatchTonumber = istMaster.TONumber;
            q.StoreBatchOwnerType = ownerType;
            q.StoreBatchOwnerCode = istMaster.fromStoreCode;
            q.StoreBatchCreatedBy = userId;
            q.StoreBatchCreatedDate = DateTime.Now;
            q.StoreBatchModifiedBy = userId;
            q.StoreBatchModifiedDate = DateTime.Now;
            q.StoreBatchIsActive = true;
            q.StoreTostatus = toStatus;
            q.TOCreatedDateAX = createdAX;
            q.TOShippedDateAX = shippedAX;
            q.TOReceivedDateAX = receivedAX;
            _context.StoreBatches.Add(q);
            _context.SaveChanges();

            foreach (var item in istMaster.istDetailDtos)
            {
                var qDet = new StoreBatchDetail();
                qDet.StoreBatchId = q.StoreBatchId;
                qDet.ItemCode = item.ItemCode;
                qDet.ItemMasterCode = item.ItemMasterCode;
                qDet.ItemQty = item.fetchQty;
                qDet.StoreBatchDetailRemarks = item.TORemarks;
                qDet.storeItemOnHandQtyAX = await getItemOnHandQtyAX(item.ItemCode!, item.ItemMasterCode!);
                item.cwhItemOnHandQtyAX = qDet.storeItemOnHandQtyAX;
                qDet.StoreBatchDetailCreatedBy = userId;
                qDet.StoreBatchDetailCreatedDate = DateTime.Now;
                qDet.StoreBatchDetailModifiedBy = userId;
                qDet.StoreBatchDetailModifiedDate = DateTime.Now;
                qDet.StoreBatchDetailIsActive = true;
                _context.StoreBatchDetails.Add(qDet);
                _context.SaveChanges();
            }

            if (isFullyCloseIST == "P")
            {
                saveIstProgress(istMaster.IstId ?? 0, 5, "IN PROCESS", userId);
            }
            else
            {
                saveIstProgress(istMaster.IstId ?? 0, 7, "CLOSED", userId);
            }

            saveStoreProgress(q.StoreBatchId, "SOURCE", toStatus, userId);

            if (istMaster.toStoreCode != "NEEM")
            {
                var qCWH = new StoreBatchesCwh();
                qCWH.IstId = istMaster.IstId;
                qCWH.storeBatchIdSource = q.StoreBatchId;
                qCWH.CwhMasterBatchCode = batchMasterCode;
                qCWH.CwhBatchCode = batchCode;
                qCWH.CwhBatchTonumber = istMaster.TONumber;
                qCWH.CwhBatchOwnerType = ownerType;
                qCWH.CwhBatchOwnerCode = istMaster.fromStoreCode;
                qCWH.CwhBatchCreatedBy = userId;
                qCWH.CwhBatchCreatedDate = DateTime.Now;
                qCWH.CwhBatchModifiedBy = userId;
                qCWH.CwhBatchModifiedDate = DateTime.Now;
                qCWH.CwhBatchIsActive = true;
                qCWH.CwhTostatus = "DISPATCHED";
                qCWH.cwhTOCreatedDateAX = createdAX;
                qCWH.cwhTOShippedDateAX = shippedAX;
                qCWH.cwhTOReceivedDateAX = receivedAX;
                _context.StoreBatchesCwhs.Add(qCWH);
                _context.SaveChanges();

                foreach (var item in istMaster.istDetailDtos)
                {
                    var qDet = new StoreBatchDetailsCwh();
                    qDet.CwhBatchId = qCWH.CwhBatchId;
                    qDet.ItemCode = item.ItemCode;
                    qDet.ItemMasterCode = item.ItemMasterCode;
                    qDet.ItemQty = item.fetchQty;
                    qDet.CwhBatchDetailRemarks = item.TORemarks;
                    qDet.cwhItemOnHandQtyAX = item.cwhItemOnHandQtyAX;
                    qDet.CwhBatchDetailCreatedBy = userId;
                    qDet.CwhBatchDetailCreatedDate = DateTime.Now;
                    qDet.CwhBatchDetailModifiedBy = userId;
                    qDet.CwhBatchDetailModifiedDate = DateTime.Now;
                    qDet.CwhBatchDetailIsActive = true;
                    _context.StoreBatchDetailsCwhs.Add(qDet);
                    _context.SaveChanges();
                }

                saveStoreProgress(q.StoreBatchId, "SOURCE", "ACCEPTED BY CWH", userId);
                saveStoreProgress(qCWH.CwhBatchId, "CWH", "DISPATCHED BY CWH", userId);
            }
            else
            {
                q.StoreTostatus = "ACCEPTED BY CWH";
                _context.SaveChanges();
                saveStoreProgress(q.StoreBatchId, "SOURCE", "ACCEPTED BY CWH", userId);
            }

            return await Task.FromResult("OK");
        }

        public async Task<string> saveTOItemsforCWH(ISTDTO istMaster, string ownerType, string toStatus, string loginId, string CWhTONumberTemp, string isSrcTOFullyClosed)
        {
            int userId = await getUserIdInt(loginId);

            var batchMasterCode = "";
            var batchCode = "";
            batchMasterCode = Guid.NewGuid().ToString();
            batchCode = batchMasterCode;

            DateTime createdAX = DateTime.Now;
            DateTime shippedAX = DateTime.Now;
            DateTime receivedAX = DateTime.Now;
            var TOData = await getTOMasterData(CWhTONumberTemp);
            if (TOData != null)
            {
                createdAX = TOData.storeTOCreatedDateAX ?? DateTime.Now;
                shippedAX = TOData.storeTOShippedDateAX ?? DateTime.Now;
                receivedAX = TOData.storeTOReceivedDateAX ?? DateTime.Now;
            }

            var q = new StoreBatchesCwh();
            q.IstId = istMaster.IstId;
            q.storeBatchIdSource = istMaster.storeBatchId;
            q.CwhMasterBatchCode = batchMasterCode;
            q.CwhBatchCode = batchCode;
            q.CwhBatchTonumber = CWhTONumberTemp;
            q.CwhBatchOwnerType = ownerType;
            q.CwhBatchOwnerCode = istMaster.fromStoreCode;
            q.CwhBatchCreatedBy = userId;
            q.CwhBatchCreatedDate = DateTime.Now;
            q.CwhBatchModifiedBy = userId;
            q.CwhBatchModifiedDate = DateTime.Now;
            q.CwhBatchIsActive = true;
            q.CwhTostatus = toStatus;
            q.cwhTOCreatedDateAX = createdAX;
            q.cwhTOShippedDateAX = shippedAX;
            q.cwhTOReceivedDateAX = receivedAX;
            _context.StoreBatchesCwhs.Add(q);
            _context.SaveChanges();

            foreach (var item in istMaster.istStoreBatchDetail)
            {
                var qDet = new StoreBatchDetailsCwh();
                qDet.CwhBatchId = q.CwhBatchId;
                qDet.ItemCode = item.ItemCode;
                qDet.ItemMasterCode = item.ItemMasterCode;
                qDet.ItemQty = item.fetchQty;
                qDet.cwhItemOnHandQtyAX = await getItemOnHandQtyAX(item.ItemCode!, item.ItemMasterCode!);
                qDet.CwhBatchDetailRemarks = item.TORemarks;
                qDet.CwhBatchDetailCreatedBy = userId;
                qDet.CwhBatchDetailCreatedDate = DateTime.Now;
                qDet.CwhBatchDetailModifiedBy = userId;
                qDet.CwhBatchDetailModifiedDate = DateTime.Now;
                qDet.CwhBatchDetailIsActive = true;
                _context.StoreBatchDetailsCwhs.Add(qDet);
                _context.SaveChanges();
            }

            if (toStatus == "DISPATCHED")
                toStatus = "DISPATCHED BY CWH";

            saveStoreProgress(q.CwhBatchId, "CWH", toStatus, userId);

            if (isSrcTOFullyClosed == "P")
                toStatus = "IN PROCESS";
            else
                toStatus = "CLOSED";

            var qStoreBatch = _context.StoreBatches.SingleOrDefault(x => x.StoreBatchId == istMaster.storeBatchId);
            qStoreBatch.StoreTostatus = toStatus;
            _context.SaveChanges();

            saveStoreProgress(istMaster.storeBatchId, "SOURCE", toStatus, userId);

            return await Task.FromResult("OK");


            //int userId = await getUserIdInt(loginId);
            //saveIstProgress(pIstMasterDto.IstId ?? 0, 7, "CLOSED", userId);
            //return await Task.FromResult("OK");
        }



        public async Task<string> receiveTOItemsForDest(ISTDTO istMaster, string ownerType, string loginId)
        {
            int userId = await getUserIdInt(loginId);

            var batchMasterCode = "";
            var batchCode = "";
            batchMasterCode = Guid.NewGuid().ToString();
            batchCode = batchMasterCode;

            var q = new StoreBatchesDest();
            q.IstId = istMaster.IstId;
            q.storeBatchIdCWH = istMaster.storeBatchIdCWH;
            q.DestMasterBatchCode = batchMasterCode;
            q.DestBatchCode = batchCode;
            q.DestBatchTonumber = istMaster.CWHTONumber;
            q.DestBatchOwnerType = ownerType;
            q.DestBatchOwnerCode = istMaster.fromStoreCode;
            q.DestBatchCreatedBy = userId;
            q.DestBatchCreatedDate = DateTime.Now;
            q.DestBatchModifiedBy = userId;
            q.DestBatchModifiedDate = DateTime.Now;
            q.DestBatchIsActive = true;
            q.DestTostatus = "RECEIVED";
            _context.StoreBatchesDests.Add(q);
            _context.SaveChanges();

            foreach (var item in istMaster.istCWHBatchDetail)
            {
                var qDet = new StoreBatchDetailsDest();
                qDet.DestBatchId = q.DestBatchId;
                qDet.ItemCode = item.ItemCode;
                qDet.ItemMasterCode = item.ItemMasterCode;
                qDet.ItemQty = item.ItemQty;
                qDet.DestBatchDetailRemarks = item.TORemarks;
                qDet.destItemOnHandQtyAX = await getItemOnHandQtyAX(item.ItemCode!, item.ItemMasterCode!);
                qDet.DestBatchDetailCreatedBy = userId;
                qDet.DestBatchDetailCreatedDate = DateTime.Now;
                qDet.DestBatchDetailModifiedBy = userId;
                qDet.DestBatchDetailModifiedDate = DateTime.Now;
                qDet.DestBatchDetailIsActive = true;
                _context.StoreBatchDetailsDests.Add(qDet);
                _context.SaveChanges();
            }

            saveStoreProgress(q.DestBatchId, ownerType, "Receive By Destination", userId);

            return await Task.FromResult("OK");
        }

        public async Task<string> dispatchedTONumber(ISTDTO pIstMasterDto, string toNumber, int storeBatchId, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            var q = _context.StoreBatches.FirstOrDefault(x => x.StoreBatchId == storeBatchId);
            q.StoreTostatus = "DISPATCHED";
            q.StoreBatchModifiedBy = userId;
            q.StoreBatchModifiedDate = DateTime.Now;
            _context.SaveChanges();

            saveIstProgress(pIstMasterDto.IstId ?? 0, 5, "IN PROCESS", userId);

            saveStoreProgress(storeBatchId, "SOURCE", "DISPATCHED", userId);

            return await Task.FromResult("OK");
        }

        public async Task<string> markCloseIST(ISTDTO pIstMasterDto, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            saveIstProgress(pIstMasterDto.IstId ?? 0, 7, "CLOSED", userId);
            return await Task.FromResult("OK");
        }

        public async Task<string> removeTONo(ISTDTO pIstMasterDto, int storeBatchId, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            var qExist = _context.StoreBatches.Any(x => x.StoreBatchId == storeBatchId);
            if (qExist)
            {
                var q = _context.StoreBatches.FirstOrDefault(x => x.StoreBatchId == storeBatchId);
                var qDet = _context.StoreBatchDetails.Where(x => x.StoreBatchId == storeBatchId).ToList();

                foreach (var item in qDet)
                    _context.StoreBatchDetails.Remove(item);

                _context.StoreBatches.Remove(q);

                _context.SaveChanges();

                saveStoreProgress(storeBatchId, "SOURCE", "REMOVED", userId);

            }
            return await Task.FromResult("OK");
        }

        public async Task<string> removeTONoByCWH(ISTDTO pIstMasterDto, string loginId, string CWhTONumberTemp)
        {
            int userId = await getUserIdInt(loginId);
            //var qExist = _context.StoreBatchesCwhs.Any(x => x.CwhBatchId == pIstMasterDto.istStoreBatch.FirstOrDefault()!.CWHBatchId);
            //if (qExist)
            //{
            //    var q = _context.StoreBatchesCwhs.FirstOrDefault(x => x.CwhBatchId == pIstMasterDto.istStoreBatch.FirstOrDefault()!.CWHBatchId);
            //    var qDet = _context.StoreBatchDetailsCwhs.Where(x => x.CwhBatchId == pIstMasterDto.istStoreBatch.FirstOrDefault()!.CWHBatchId).ToList();

            //    foreach (var item in qDet)
            //        _context.StoreBatchDetailsCwhs.Remove(item);

            //    _context.StoreBatchesCwhs.Remove(q);

            //    _context.SaveChanges();

            //    saveStoreProgress(pIstMasterDto.istStoreBatch.FirstOrDefault()!.CWHBatchId ?? 0, "CWH", "REMOVED", userId);

            //}
            return await Task.FromResult("OK");
        }
        public async Task<List<ISTDTO>> getISTForCWH(string loginId, filterForm filters)
        {
            int userId = await getUserIdInt(loginId);

            var q = (from a in _context.Ists
                     join fromStores in _context.Stores on a.IstFromStoreId equals fromStores.Id
                     join toStores in _context.Stores on a.IstToStoreId equals toStores.Id
                     join uCreatedBy in _context.Users on a.IstCreatedBy equals uCreatedBy.Id
                     join uModifiedBy in _context.Users on a.IstModifiedBy equals uModifiedBy.Id
                     join storeBatchSrc in _context.StoreBatches on a.IstId equals storeBatchSrc.IstId
                     where a.IstIsActive == true && storeBatchSrc.StoreTostatus != "DRAFT" && toStores.Code == "NEEM"
                     orderby a.IstCreatedDate descending
                     select new ISTDTO
                     {
                         IstId = a.IstId,
                         IstCode = a.IstCode,
                         IstTitle = a.IstTitle,
                         IstDescription = a.IstDescription,
                         IstFromStoreId = a.IstFromStoreId,
                         fromStore = fromStores.Name,
                         fromStoreCode = fromStores.Code,
                         IstToStoreId = a.IstToStoreId,
                         toStore = toStores.Name,
                         toStoreCode = toStores.Code,
                         TotalItems = _context.IstDetails.Count(x => x.IstId == a.IstId),
                         TotalQty = _context.IstDetails.Where(x => x.IstId == a.IstId).Sum(x => x.ItemQty),
                         ISTStatus = _context.IstProgresses.Where(x => x.IstId == a.IstId).OrderByDescending(x => x.IstProgressCreatedDate).Select(x => x.IstProgressName).FirstOrDefault(),
                         IstCreatedByString = uCreatedBy.UserId,
                         IstCreatedDate = a.IstCreatedDate,
                         IstModifiedByString = uModifiedBy.UserId,
                         IstModifiedDate = a.IstModifiedDate,
                         IstActive = ((bool)a.IstIsActive! ? "Yes" : "No"),
                         IstIsActive = a.IstIsActive,
                         TONumber = storeBatchSrc.StoreBatchTonumber,
                         storeBatchId = storeBatchSrc.StoreBatchId,
                     }).ToList();

            var validStoreVBatches = q.Select(x => x.storeBatchId).ToList();
            var storebatchSrc = _context.StoreBatches
                                .Where(x => validStoreVBatches.Contains(x.StoreBatchId))
                                  .Select(x => new StoreBatchDTO
                                  {
                                      StoreBatchId = x.StoreBatchId,
                                      IstId = x.IstId,
                                      StoreMasterBatchCode = x.StoreMasterBatchCode,
                                      StoreBatchCode = x.StoreBatchCode,
                                      StoreBatchTonumber = x.StoreBatchTonumber,
                                      StoreBatchOwnerType = x.StoreBatchOwnerType,
                                      StoreBatchOwnerCode = x.StoreBatchOwnerCode,
                                      StoreBatchIsActive = x.StoreBatchIsActive,
                                      TotalItems = _context.StoreBatchDetails.Count(a => a.StoreBatchId == x.StoreBatchId),
                                      TotalQty = _context.StoreBatchDetails.Where(a => a.StoreBatchId == x.StoreBatchId).Sum(x => x.ItemQty),
                                      StoreBatchCreatedByString = _context.Users.FirstOrDefault(a => a.Id == x.StoreBatchCreatedBy)!.UserId,
                                      StoreBatchCreatedDate = x.StoreBatchCreatedDate,
                                      StoreBatchModifiedByString = _context.Users.FirstOrDefault(a => a.Id == x.StoreBatchModifiedBy)!.UserId,
                                      StoreBatchModifiedDate = x.StoreBatchModifiedDate,
                                      storeTOStatus = x.StoreTostatus,
                                      TOCreatedDateAX = x.TOCreatedDateAX ?? DateTime.Now,
                                      TOShippedDateAX = x.TOShippedDateAX ?? DateTime.Now,
                                      TOReceivedDateAX = x.TOReceivedDateAX ?? DateTime.Now,
                                      //CWHToNumber = _context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.CwhBatchTonumber ?? "",
                                      //CWHToStatus = _context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.CwhTostatus ?? "",
                                      //CWHBatchId = (int?)_context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.CwhBatchId ?? 0,
                                      //cwhTOCreatedDateAX = _context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.cwhTOCreatedDateAX ?? DateTime.Now,
                                      //cwhTOShippedDateAX = _context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.cwhTOShippedDateAX ?? DateTime.Now,
                                      //cwhTOReceivedDateAX = _context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.cwhTOReceivedDateAX ?? DateTime.Now,
                                  }).ToList();

            foreach (var item in q)
            {
                var sBatchExist = storebatchSrc.Any(x => x.StoreBatchId == item.storeBatchId);
                if (sBatchExist)
                {
                    item.istStoreBatch = storebatchSrc.Where(x => x.StoreBatchId == item.storeBatchId).ToList();
                }
            }

            foreach (var item in storebatchSrc)
            {
                var qCWH = (from a in _context.StoreBatchesCwhs
                            where a.storeBatchIdSource == item.StoreBatchId
                            select new StoreBatchCWHDTO
                            {
                                StoreBatchIdSrc = item.StoreBatchId,
                                CWHToNumber = a.CwhBatchTonumber ?? "",
                                CWHToStatus = a.CwhTostatus ?? "",
                                CWHBatchId = a.CwhBatchId,
                                cwhTOCreatedDateAX = a.cwhTOCreatedDateAX ?? DateTime.Now,
                                cwhTOShippedDateAX = a.cwhTOShippedDateAX ?? DateTime.Now,
                                cwhTOReceivedDateAX = a.cwhTOReceivedDateAX ?? DateTime.Now,
                                storeCreatedByStrCWH = _context.Users.FirstOrDefault(x => x.Id == a.CwhBatchCreatedBy)!.UserId,
                                storeCreatedDateCWH = a.CwhBatchCreatedDate,
                            }).ToList();
                if (qCWH.Any())
                    item.storeBatchCWH = qCWH;
            }



            return await Task.FromResult(q);
        }

        public async Task<string> dispatchTOCWH(ISTDTO pIstMaster, string loginId)
        {
            int userId = await getUserIdInt(loginId);
            //var q = _context.StoreBatchesCwhs.FirstOrDefault(x => x.CwhBatchId == pIstMaster.istStoreBatch.FirstOrDefault()!.CWHBatchId);
            //q.CwhTostatus = "DISPATCHED";
            //q.CwhBatchModifiedBy = userId;
            //q.CwhBatchModifiedDate = DateTime.Now;
            //_context.SaveChanges();

            //saveStoreProgress(pIstMaster.istStoreBatch.FirstOrDefault()!.CWHBatchId ?? 0, "CWH", "DISPATCHED BY CWH", userId);

            return await Task.FromResult("OK");
        }

        public async Task<List<ISTDTO>> getISTForDEST(string loginId, filterForm filters)
        {
            int userId = await getUserIdInt(loginId);

            var q = (from a in _context.Ists
                     join fromStores in _context.Stores on a.IstFromStoreId equals fromStores.Id
                     join toStores in _context.Stores on a.IstToStoreId equals toStores.Id
                     join uCreatedBy in _context.Users on a.IstCreatedBy equals uCreatedBy.Id
                     join uModifiedBy in _context.Users on a.IstModifiedBy equals uModifiedBy.Id
                     join storeBatchSrc in _context.StoreBatches on a.IstId equals storeBatchSrc.IstId
                     join storeBatchCWH in _context.StoreBatchesCwhs on storeBatchSrc.StoreBatchId equals storeBatchCWH.storeBatchIdSource
                     where a.IstIsActive == true && storeBatchCWH.CwhTostatus == "DISPATCHED"
                     orderby a.IstCreatedDate descending
                     select new ISTDTO
                     {
                         IstId = a.IstId,
                         IstCode = a.IstCode,
                         IstTitle = a.IstTitle,
                         IstDescription = a.IstDescription,
                         IstFromStoreId = a.IstFromStoreId,
                         fromStore = fromStores.Name,
                         fromStoreCode = fromStores.Code,
                         IstToStoreId = a.IstToStoreId,
                         toStore = toStores.Name,
                         toStoreCode = toStores.Code,
                         TotalItems = _context.IstDetails.Count(x => x.IstId == a.IstId),
                         TotalQty = _context.IstDetails.Where(x => x.IstId == a.IstId).Sum(x => x.ItemQty),
                         ISTStatus = _context.IstProgresses.Where(x => x.IstId == a.IstId).OrderByDescending(x => x.IstProgressCreatedDate).Select(x => x.IstProgressName).FirstOrDefault(),
                         IstCreatedByString = uCreatedBy.UserId,
                         IstCreatedDate = a.IstCreatedDate,
                         IstModifiedByString = uModifiedBy.UserId,
                         IstModifiedDate = a.IstModifiedDate,
                         IstActive = ((bool)a.IstIsActive! ? "Yes" : "No"),
                         IstIsActive = a.IstIsActive,
                         TONumber = storeBatchSrc.StoreBatchTonumber,
                         storeBatchId = storeBatchSrc.StoreBatchId,
                         storeBatchIdCWH = storeBatchCWH.CwhBatchId,
                         SourceTONumber = storeBatchSrc.StoreBatchTonumber,
                         SourceTOStatus = storeBatchSrc.StoreTostatus,
                         CWHTONumber = storeBatchCWH.CwhBatchTonumber,
                         CWHTOStatus = storeBatchCWH.CwhTostatus,
                         SrcTotalItems = _context.StoreBatchDetails.Count(a => a.StoreBatchId == storeBatchSrc.StoreBatchId),
                         SrcTotalQty = _context.StoreBatchDetails.Where(a => a.StoreBatchId == storeBatchSrc.StoreBatchId).Sum(x => x.ItemQty) ?? 0,
                         CWHTotalItems = _context.StoreBatchDetailsCwhs.Count(a => a.CwhBatchId == storeBatchCWH.CwhBatchId),
                         CWHTotalQty = _context.StoreBatchDetailsCwhs.Where(a => a.CwhBatchId == storeBatchCWH.CwhBatchId).Sum(x => x.ItemQty) ?? 0,
                         destToStatus = _context.StoreBatchesDests.FirstOrDefault(x => x.storeBatchIdCWH == storeBatchCWH.CwhBatchId)!.DestTostatus ?? "",
                     }).ToList();

            //var validStoreVBatches = q.Select(x => x.storeBatchId).ToList();
            //var storebatchSrc = _context.StoreBatches
            //                    .Where(x => validStoreVBatches.Contains(x.StoreBatchId))
            //                      .Select(x => new StoreBatchDTO
            //                      {
            //                          StoreBatchId = x.StoreBatchId,
            //                          IstId = x.IstId,
            //                          StoreMasterBatchCode = x.StoreMasterBatchCode,
            //                          StoreBatchCode = x.StoreBatchCode,
            //                          StoreBatchTonumber = x.StoreBatchTonumber,
            //                          StoreBatchOwnerType = x.StoreBatchOwnerType,
            //                          StoreBatchOwnerCode = x.StoreBatchOwnerCode,
            //                          StoreBatchIsActive = x.StoreBatchIsActive,
            //                          TotalItems = _context.StoreBatchDetails.Count(a => a.StoreBatchId == x.StoreBatchId),
            //                          TotalQty = _context.StoreBatchDetails.Where(a => a.StoreBatchId == x.StoreBatchId).Sum(x => x.ItemQty),
            //                          StoreBatchCreatedByString = _context.Users.FirstOrDefault(a => a.Id == x.StoreBatchCreatedBy)!.UserId,
            //                          StoreBatchCreatedDate = x.StoreBatchCreatedDate,
            //                          StoreBatchModifiedByString = _context.Users.FirstOrDefault(a => a.Id == x.StoreBatchModifiedBy)!.UserId,
            //                          StoreBatchModifiedDate = x.StoreBatchModifiedDate,
            //                          storeTOStatus = x.StoreTostatus,
            //                          //CWHToNumber = _context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.CwhBatchTonumber ?? "",
            //                          //CWHToStatus = _context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.CwhTostatus ?? "",
            //                          //CWHBatchId = (int?)_context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.CwhBatchId ?? 0,
            //                          //CWHTotalItems = _context.StoreBatchDetailsCwhs
            //                          //                  .Count(a => a.CwhBatchId == (int?)_context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.CwhBatchId),
            //                          //CWHTotalQty = _context.StoreBatchDetailsCwhs
            //                          //                  .Where(a => a.CwhBatchId == (int?)_context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.CwhBatchId)
            //                          //                  .Sum(a => a.ItemQty),
            //                          destToStatus = _context.StoreBatchesDests
            //                                            .FirstOrDefault(a => a.storeBatchIdCWH == (int?)_context.StoreBatchesCwhs.FirstOrDefault(a => a.storeBatchIdSource == x.StoreBatchId)!.CwhBatchId)!.DestTostatus,
            //                      }).ToList();

            //foreach (var item in q)
            //{
            //    var sBatchExist = storebatchSrc.Any(x => x.StoreBatchId == item.storeBatchId);
            //    if (sBatchExist)
            //    {
            //        item.istStoreBatch = storebatchSrc.Where(x => x.StoreBatchId == item.storeBatchId).ToList();
            //    }
            //}

            return await Task.FromResult(q);
        }

        #region "REPORTS SP"
        public async Task<List<ISTReportDTO>> getISTReport(filterForm filters)
        {
            await using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            var parameters = new DynamicParameters();
            parameters.Add("@FROMDATE", filters.fromDate);
            parameters.Add("@TODATE", filters.toDate);
            parameters.Add("@ISTCODE", filters.ISTCode);

            var result = await connection.QueryAsync<ISTReportDTO>(
                "dbo.SP_RPT_ISTInfo",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            result = result.ToList();

            if (!result.Any())
                throw new Exception("Record not found!");
            return (List<ISTReportDTO>)result;
        }

        public async Task<List<IstTimeComplianceDto>> getISTTimeComplianceReport(filterForm filters)
        {
            await using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            var parameters = new DynamicParameters();
            parameters.Add("@FROMDATE", filters.fromDate);
            parameters.Add("@TODATE", filters.toDate);
            parameters.Add("@ISTID", filters.ISTCode);

            var result = await connection.QueryAsync<IstTimeComplianceDto>(
                "dbo.proc_rpt_istTimeCompliane",
                parameters,
                commandType: CommandType.StoredProcedure
            );

            result = result.ToList();

            if (!result.Any())
                throw new Exception("Record not found!");
            return (List<IstTimeComplianceDto>)result;
        }

        //public async Task<List<IstTrackingDto>> getISTTrackingReport(string istCode)
        //{
        //    await using var connection = new SqlConnection(_connectionString);
        //    await connection.OpenAsync();
        //    var parameters = new DynamicParameters();
        //    parameters.Add("@ISTCODE", istCode);

        //    var result = await connection.QueryAsync<IstTrackingDto>(
        //        "dbo.proc_rpt_istTracking",
        //        parameters,
        //        commandType: CommandType.StoredProcedure
        //    );

        //    var list = result.ToList();
        //    if (!list.Any())
        //        throw new Exception("Record not found!");
        //    return list;
        //}
        #endregion
    }
}
