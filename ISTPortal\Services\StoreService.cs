using ISTPortal.Data;
using ISTPortal.DTO;
using ISTPortal.Models;
using Microsoft.EntityFrameworkCore;

namespace ISTPortal.Services
{
    public class StoreService
    {
        private readonly ApplicationDbContext _context;

        public StoreService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<StoreDTO>> GetAllStoresAsync()
        {
            try
            {
                return await _context.Stores
                .Where(s => s.IsActive)
                .OrderBy(s => s.Name)
                .Select(s => new StoreDTO
                {
                    Id = s.Id,
                    Code = s.Code,
                    Name = s.Name,
                    Address = s.Address ?? "",
                    Notes = s.Notes ?? "",
                    Manager = s.Manager ?? "",
                    ContactNumber = s.ContactNumber ?? "",
                    IsActive = s.IsActive
                })
                .ToListAsync();
            }
            catch (Exception)
            {

                //throw;
            }

            return new List<StoreDTO>();
            
        }

        public async Task<StoreDTO?> GetStoreByIdAsync(int id)
        {
            return await _context.Stores
                .Where(s => s.Id == id)
                .Select(s => new StoreDTO
                {
                    Id = s.Id,
                    Code = s.Code,
                    Name = s.Name,
                    Address = s.Address ?? "",
                    Notes = s.Notes ?? "",
                    Manager = s.Manager ?? "",
                    ContactNumber = s.ContactNumber ?? "",
                    IsActive = s.IsActive
                })
                .FirstOrDefaultAsync();
        }

        public async Task<bool> CreateStoreAsync(StoreDTO storeDto, int currentUserId)
        {
            try
            {
                var store = new Store
                {
                    Code = storeDto.Code,
                    Name = storeDto.Name,
                    Address = storeDto.Address,
                    Notes = storeDto.Notes,
                    Manager = storeDto.Manager,
                    ContactNumber = storeDto.ContactNumber,
                    IsActive = storeDto.IsActive,
                    CreatedDate = DateTime.Now,
                    CreatedBy = currentUserId,
                    ModifiedDate = DateTime.Now,
                    ModifiedBy = currentUserId
                };

                _context.Stores.Add(store);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateStoreAsync(StoreDTO storeDto, int currentUserId)
        {
            try
            {
                var store = await _context.Stores.FindAsync(storeDto.Id);
                if (store == null) return false;

                store.Code = storeDto.Code;
                store.Name = storeDto.Name;
                store.Address = storeDto.Address;
                store.Notes = storeDto.Notes;
                store.Manager = storeDto.Manager;
                store.ContactNumber = storeDto.ContactNumber;
                store.IsActive = storeDto.IsActive;
                store.ModifiedDate = DateTime.Now;
                store.ModifiedBy = currentUserId;

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteStoreAsync(int id, int currentUserId)
        {
            try
            {
                var store = await _context.Stores.FindAsync(id);
                if (store == null) return false;

                store.IsActive = false;
                store.ModifiedDate = DateTime.Now;
                store.ModifiedBy = currentUserId;

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> StoreCodeExistsAsync(string code, int? excludeId = null)
        {
            return await _context.Stores
                .AnyAsync(s => s.Code == code && s.IsActive && (excludeId == null || s.Id != excludeId));
        }

        public async Task<bool> StoreNameExistsAsync(string name, int? excludeId = null)
        {
            return await _context.Stores
                .AnyAsync(s => s.Name == name && s.IsActive && (excludeId == null || s.Id != excludeId));
        }
    }
}
