﻿namespace ISTPortal.DTO
{
    public class StoreBatchCWHDTO
    {
        public int StoreBatchIdSrc { get; set; }
        public int? CWHBatchId { get; set; }
        public string CWHToNumber { get; set; }
        public string CWHToStatus { get; set; }
        public int? CWHTotalItems { get; set; }
        public int? CWHTotalQty { get; set; }
        public DateTime? cwhTOCreatedDateAX { get; set; }
        public DateTime? cwhTOShippedDateAX { get; set; }
        public DateTime? cwhTOReceivedDateAX { get; set; }
        public string? storeCreatedByStrCWH { get; set; }
        public DateTime? storeCreatedDateCWH { get; set; }
    }
}
