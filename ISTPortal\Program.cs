using ISTPortal.Components;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor;
using Microsoft.AspNetCore.Components.Authorization;
using ISTPortal.Components.Authentication;
using Microsoft.EntityFrameworkCore;
using ISTPortal.Models;
using ISTPortal.Data;
using ISTPortal.Services;
using Syncfusion.Blazor.Popups;
using Microsoft.AspNetCore.Authentication.Cookies;

namespace ISTPortal
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzY2NDQzOUAzMjM4MmUzMDJlMzBkT1liN3Z3aGwzb0c4YmNNNUhOd2NzWHp6RUFVVGVNL0Y1Z2x3bW1FYzJJPQ==");

            builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

            builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
               .AddCookie(c =>
               {
                   c.Cookie.Name = "auth_token";
                   c.Cookie.MaxAge = TimeSpan.FromMinutes(120);
                   c.LoginPath = "/login";
                   c.AccessDeniedPath = "/account/access-denied";
               });
            builder.Services.AddAuthorization();
            builder.Services.AddCascadingAuthenticationState();

            // Add DbContext
            builder.Services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

            builder.Services.AddFluentUIComponents();
            builder.Services.AddSyncfusionBlazor();

            builder.Services.AddScoped<ISTDataService>();
            builder.Services.AddScoped<SfDialogService>();
            builder.Services.AddScoped<UserService>();
            builder.Services.AddScoped<AuthenticationService>();
            builder.Services.AddScoped<Services.MenuService>();
            builder.Services.AddScoped<StoreService>();
            builder.Services.AddScoped<UserRightsService>();

            var app = builder.Build();

            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Error");
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseAntiforgery();

            app.MapRazorComponents<App>()
                .AddInteractiveServerRenderMode();

            app.Run();
        }
    }
}
