﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ISTPortal.Models;

public partial class StoreBatchesCwh
{
    public int CwhBatchId { get; set; }

    public int? IstId { get; set; }

    public int storeBatchIdSource { get; set; }
    public string CwhMasterBatchCode { get; set; }

    public string CwhBatchCode { get; set; }

    public string CwhBatchTonumber { get; set; }

    public string CwhBatchOwnerType { get; set; }

    public string CwhBatchOwnerCode { get; set; }

    public DateTime? CwhBatchCreatedDate { get; set; }

    public int? CwhBatchCreatedBy { get; set; }

    public DateTime? CwhBatchModifiedDate { get; set; }

    public int? CwhBatchModifiedBy { get; set; }

    public bool? CwhBatchIsActive { get; set; }

    public string CwhTostatus { get; set; }

    public DateTime? cwhTOCreatedDateAX { get; set; }
    public DateTime? cwhTOShippedDateAX { get; set; }
    public DateTime? cwhTOReceivedDateAX { get; set; }
}