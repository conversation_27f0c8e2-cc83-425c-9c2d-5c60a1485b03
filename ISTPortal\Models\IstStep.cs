﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ISTPortal.Models;

public partial class IstStep
{
    public int IstStepId { get; set; }

    public string IstStepCode { get; set; }

    public string IstStepName { get; set; }

    public string IstStepDesc { get; set; }

    public int? IstStepSortOrder { get; set; }

    public string IstStepOwner { get; set; }

    public DateTime? IstStepCreatedDate { get; set; }

    public int? IstStepCreatedBy { get; set; }

    public DateTime? IstStepModifiedDate { get; set; }

    public int? IstStepModifiedBy { get; set; }

    public bool? IstStepIsActive { get; set; }
}