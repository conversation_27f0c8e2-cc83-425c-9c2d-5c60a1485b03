﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace ISTPortal.Models;

public partial class User
{
    public int Id { get; set; }

    public string UserId { get; set; }

    public string Password { get; set; }

    public string FullName { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public string CreatedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string ModifiedBy { get; set; }

    [NotMapped]
    public virtual ICollection<UserMenu> UserMenus { get; set; } = new List<UserMenu>();
}