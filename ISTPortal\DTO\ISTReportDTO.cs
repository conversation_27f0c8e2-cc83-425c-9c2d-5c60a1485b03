﻿namespace ISTPortal.DTO
{
    public class ISTReportDTO
    {
        public int istID { get; set; } = 0;
        public string istCode { get; set; } = "";
        public string istTitle { get; set; } = "";
        public string istItemCode { get; set; } = "";
        public string istItemMasterCode { get; set; } = "";
        public int istItemQty { get; set; } = 0;
        public string sendItemCode { get; set; } = "";
        public string sendItemMasterCode { get; set; } = "";
        public int sendItemQty { get; set; } = 0;
        public int sendItemQtyAX { get; set; } = 0;
        public string cwhItemCode { get; set; } = "";
        public string cwhItemMasterCode { get; set; } = "";
        public int cwhItemQty { get; set; } = 0;
        public string dstItemCode { get; set; } = "";
        public string dstItemMasterCode { get; set; } = "";
        public int dstItemQty { get; set; } = 0;
        public string ACCURACRY_COMPLIANCE { get; set; } = "";
        public string QTY_COMPLIANCE { get; set; } = "";
        public string STOCK_COMPLIANCE { get; set; } = "";

    }
}
