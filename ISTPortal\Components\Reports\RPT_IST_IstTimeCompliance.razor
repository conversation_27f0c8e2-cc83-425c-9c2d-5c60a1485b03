﻿@page "/reports/rptisttimecompliance"
@using ISTPortal.DTO
@using ISTPortal.Services
@using Syncfusion.Blazor.Cards
@using Syncfusion.Blazor.Grids
@inject Microsoft.FluentUI.AspNetCore.Components.IDialogService DialogService
@inject Syncfusion.Blazor.Popups.SfDialogService sfDialogService
@inject NavigationManager NavigationManager
@inject ISTDataService istService
@inject IJSRuntime JS

@rendermode InteractiveServer

<style>
    .componentWidth {
    width: -webkit-fill-available;
    }

    label {
    padding-left: 2px;
    }

    .cardrow {
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px;
    margin: 0px;
    background-color: #036ac4;
    color: white;
    }

    .file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    }

    .e-grid td.e-active {
    background: powderblue !important;
    }
</style>

<FluentBreadcrumb>
    <FluentBreadcrumbItem Href="/">
        <FluentIcon Value="@(new Icons.Regular.Size16.Home())" Color="@Color.Neutral" Slot="start" />
    </FluentBreadcrumbItem>
    <FluentBreadcrumbItem Href="/reports/rptisttimecompliance">
        Report - IST Time Compliance
    </FluentBreadcrumbItem>
</FluentBreadcrumb>

<div class="row mt-2">
    <div class="col-2">
        <FluentDatePicker Label="Start Date" AriaLabel="Start Date" Class="componentWidth" @bind-Value="@filter.fromDate" />
    </div>
    <div class="col-2">
        <FluentDatePicker Label="End Date" Class="componentWidth" AriaLabel="End Date" @bind-Value="@filter.toDate" />
    </div>
    <div class="col-3">
        <FluentTextField Class="componentWidth" @bind-Value="@filter.ISTCode" Label="IST Code"></FluentTextField>
    </div>
    <div class="col-3">

    </div>
</div>

<FluentStack HorizontalGap="15" Class="mt-2">
    <FluentButton IconStart="@(new Icons.Regular.Size16.ContentView())"
    Appearance="Appearance.Accent" OnClick="viewISTTimeComplianceReport">
        View Report
    </FluentButton>
</FluentStack>

<SfGrid @ref="dgISTTimeComplianceRpt"
        DataSource="@istTimeComplianceReportData"
AllowFiltering="true">
    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn Field=@nameof(IstTimeComplianceDto.IstCode) HeaderText="IST Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.IstTitle) HeaderText="IST Title" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.IstCreatedDate) HeaderText="IST Created Date" Format="dd/MM/yyyy" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.IstPriority) HeaderText="IST Priority" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.FromStoreCode) HeaderText="From Store Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.FromStoreName) HeaderText="From Store Name" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.ToStoreCode) HeaderText="To Store Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.ToStoreName) HeaderText="To Store Name" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.DratDate) HeaderText="Draft Date" Format="dd/MM/yyyy" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.Draft) HeaderText="Draft" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.SentDate) HeaderText="Sent Date" Format="dd/MM/yyyy" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.Sent) HeaderText="Sent" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.CencelledDate) HeaderText="Cancelled Date" Format="dd/MM/yyyy" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.Cancelled) HeaderText="Cancelled" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.ClosedDate) HeaderText="Closed Date" Format="dd/MM/yyyy" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.Closed) HeaderText="Closed" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.SentToCancel) HeaderText="Sent To Cancel (Days)" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.SentToClosed) HeaderText="Sent To Closed (Days)" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.CountOffDates) HeaderText="Count Off Dates" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.IstAllowedDaysValue) HeaderText="IST Allowed Days" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(IstTimeComplianceDto.TimeCompliance) HeaderText="Time Compliance" AutoFit="true">
        </GridColumn>
    </GridColumns>
</SfGrid>

@code {
    private filterForm filter = new();
    private List<IstTimeComplianceDto> istTimeComplianceReportData = new();
    private SfGrid<IstTimeComplianceDto>? dgISTTimeComplianceRpt;

    private string loginId = "admin";
    protected override async Task OnInitializedAsync()
    {
        try
        {
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message.ToString());
        }
    }

    private async void viewISTTimeComplianceReport(){
        try
        {
            if (filter.fromDate == null || filter.toDate == null)
            {
                await sfDialogService.AlertAsync("Info", "Please select both start and end dates.");
                return;
            }
            if (filter.fromDate > filter.toDate)
            {
                await sfDialogService.AlertAsync("Info", "Start date cannot be later than end date.");
                return;
            }
            istTimeComplianceReportData = await istService.getISTTimeComplianceReport(filter);
            this.StateHasChanged();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync("Error", $"An error occurred while fetching the report: {ex.Message}");
        }
    }

}
