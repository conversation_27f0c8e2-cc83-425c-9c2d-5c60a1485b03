﻿namespace ISTPortal.DTO
{
    public class ISTDetailDTO
    {
        public int? IstDetailId { get; set; }
        public int? IstId { get; set; }
        public string? ItemCode { get; set; }
        public string? ItemMasterCode { get; set; }
        public int? ItemQty { get; set; }
        public bool? IstDetailIsActive { get; set; }
        public int? fetchQty { get; set; } = 0;
        public string? TORemarks { get; set; } = "";
        public string? HIR1 { get; set; } = "";
        public string? HIR2 { get; set; } = "";
        public string? HIR3 { get; set; } = "";
        public string? ItemDesc { get; set; } = "";
        public int cwhItemOnHandQtyAX { get; set; } = 0;
    }
}
