﻿@page "/"
@using Syncfusion.Blazor.Inputs
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@rendermode InteractiveServer
@inject NavigationManager NavigationManager
@attribute [Microsoft.AspNetCore.Authorization.Authorize] 

<PageTitle>Home - IST Portal</PageTitle>


<div class="home-container">
    <div class="welcome-section">
        <h1>Welcome to IST Portal</h1>
         <AuthorizeView>
            <Authorized>
                <p class="user-info">User ID: @context.User.Identity.Name</p>
            </Authorized>
        </AuthorizeView>
    </div>

    <div class="dashboard-cards">
        <div class="card">
            <div class="card-header">
                <h3>Quick Actions</h3>
            </div>
            <div class="card-content">
                <a href="/planning/istmaster" class="action-button">
                    <i class="icon">📋</i>
                    <span>IST Planning</span>
                </a>
                <a href="/setup/users" class="action-button">
                    <i class="icon">👥</i>
                    <span>User Management</span>
                </a>
                <a href="/setup/stores" class="action-button">
                    <i class="icon">🏪</i>
                    <span>Store Management</span>
                </a>
                <a href="/reports/rptistinfo" class="action-button">
                    <i class="icon">📊</i>
                    <span>Reports</span>
                </a>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3>System Information</h3>
            </div>
            <div class="card-content">
                <div class="info-item">
                    <span class="label">Login Time:</span>
                    <span class="value">@DateTime.Now.ToString("MMM dd, yyyy HH:mm")</span>
                </div>
                <div class="info-item">
                    <span class="label">Session Status:</span>
                    <span class="value status-active">Active</span>
                </div>
            </div>
        </div>
    </div>

    <div class="logout-section">
        @*<button class="logout-button" @onclick="HandleLogout">
                <i class="icon">🚪</i>
                Logout
            </button>*@
    </div>
</div>

<style>
    .home-container {
        padding: 2rem;
        max-width: 1200px;
        margin: 0 auto;
    }

    .welcome-section {
        text-align: center;
        margin-bottom: 3rem;
        padding: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        color: white;
    }

        .welcome-section h1 {
            margin: 0 0 1rem 0;
            font-size: 2.5rem;
            font-weight: 300;
        }

    .welcome-message {
        font-size: 1.2rem;
        margin: 0.5rem 0;
    }

    .user-info {
        opacity: 0.9;
        margin: 0;
    }

    .dashboard-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

    .card-header {
        background: #f8f9fa;
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
    }

        .card-header h3 {
            margin: 0;
            color: #495057;
            font-weight: 600;
        }

    .card-content {
        padding: 1.5rem;
    }

    .action-button {
        display: flex;
        align-items: center;
        padding: 1rem;
        margin-bottom: 0.5rem;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        text-decoration: none;
        color: #495057;
        transition: all 0.2s ease;
    }

        .action-button:hover {
            background: #e9ecef;
            transform: translateX(4px);
            text-decoration: none;
            color: #495057;
        }

        .action-button .icon {
            font-size: 1.5rem;
            margin-right: 1rem;
        }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f1f3f4;
    }

        .info-item:last-child {
            border-bottom: none;
        }

    .label {
        font-weight: 600;
        color: #6c757d;
    }

    .value {
        color: #495057;
    }

    .status-active {
        color: #28a745;
        font-weight: 600;
    }

    .logout-section {
        text-align: center;
        margin-top: 2rem;
    }

    .logout-button {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 2rem;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

        .logout-button:hover {
            background: #c82333;
        }

        .logout-button .icon {
            margin-right: 0.5rem;
        }

    @@media (max-width: 768px) {
        .home-container {
            padding: 1rem;
        }

        .welcome-section h1 {
            font-size: 2rem;
        }

        .dashboard-cards {
            grid-template-columns: 1fr;
        }
    }
</style>

@code {
    //private async Task HandleLogout()
    //{
    //    try
    //    {
    //        var success = await CustomAuthProvider.SignOutAsync();
    //        if (success)
    //        {
    //            NavigationManager.NavigateTo("/login", replace: true);
    //        }
    //    }
    //    catch (Exception ex)
    //    {
    //        Console.WriteLine($"Logout error: {ex.Message}");
    //        // Force navigation even if logout fails
    //        NavigationManager.NavigateTo("/login", replace: true);
    //    }
    //}
}
