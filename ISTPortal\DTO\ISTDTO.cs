﻿using ISTPortal.Models;
using System.ComponentModel.DataAnnotations;

namespace ISTPortal.DTO
{
    public class ISTDTO
    {
        public int? IstId { get; set; }
        [Required]
        public string? IstCode { get; set; }
        [Required]
        public string? IstTitle { get; set; }
        public string? IstDescription { get; set; }
        public int? IstFromStoreId { get; set; }
        public string? fromStore { get; set; }
        public string? fromStoreCode { get; set; }
        public int? IstToStoreId { get; set; }
        public string? toStore { get; set; }
        public string? toStoreCode { get; set; }
        public int? TotalQty { get; set; }
        public int? TotalItems { get; set; }
        public string? ISTStatus { get; set; }
        public int? ISTStatusId { get; set; }
        public DateTime? IstCreatedDate { get; set; }
        public int? IstCreatedBy { get; set; }
        public string? IstCreatedByString { get; set; }
        public DateTime? IstModifiedDate { get; set; }
        public int? IstModifiedBy { get; set; }
        public string? IstModifiedByString { get; set; }
        public bool? IstIsActive { get; set; }
        public string? IstActive { get; set; } = "";
                public string? TONumber { get; set; } = "";
        public List<ISTDetailDTO> istDetailDtos { get; set; } = new();

        public List<StoreBatchDTO> istStoreBatch { get; set; } = new();
        public int storeBatchId { get; set; }
        public List<StoreBatchDetailDTO> istStoreBatchDetail { get; set; } = new();

        public List<CWHBatchDetailDTO> istCWHBatchDetail { get; set; } = new();
        public int storeBatchIdCWH { get; set; }

        public string istPriority { get; set; } = "Normal";

        public int istInputMode { get; set; } = 1;

        public string SourceTONumber { get; set; }
        public string SourceTOStatus { get; set; }
        public string CWHTONumber { get; set; }
        public string CWHTOStatus { get; set; }
        public int SrcTotalItems { get; set; } = 0;
        public int SrcTotalQty { get; set; } = 0;
        public int CWHTotalItems { get; set; } = 0;
        public int CWHTotalQty { get; set; } = 0;
        public string destToStatus { get; set; } = "";

    }
}
