﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ISTPortal.Models;

public partial class StoreBatchDetailsCwh
{
    public int CwhBatchDetailId { get; set; }

    public int? CwhBatchId { get; set; }

    public int? ItemId { get; set; }

    public string ItemCode { get; set; }

    public string ItemMasterCode { get; set; }

    public string ItemName { get; set; }

    public int? ItemQty { get; set; }

    public string CwhBatchDetailRemarks { get; set; }

    public DateTime? CwhBatchDetailCreatedDate { get; set; }

    public int? CwhBatchDetailCreatedBy { get; set; }

    public DateTime? CwhBatchDetailModifiedDate { get; set; }

    public int? CwhBatchDetailModifiedBy { get; set; }

    public bool? CwhBatchDetailIsActive { get; set; }
    public int cwhItemOnHandQtyAX { get; set; } = 0;
}