<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="DTO\IstTrackingDto.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Components\Pages\Login.razor" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="dapper" Version="2.1.66" />
    <PackageReference Include="EPPlus" Version="6.2.10" />
    <PackageReference Include="microsoft.entityframeworkcore" Version="9.0.4" />
    <PackageReference Include="microsoft.entityframeworkcore.design" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="microsoft.entityframeworkcore.sqlserver" Version="9.0.4" />
    <PackageReference Include="microsoft.entityframeworkcore.tools" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.11.5" />
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.11.5" />
    <PackageReference Include="syncfusion.blazor" Version="28.1.38" />
    <PackageReference Include="syncfusion.blazor.themes" Version="28.1.38" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\styles\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="DTO\IstTrackingDto.cs" />
    <None Include="efpt.config.json.user" />
  </ItemGroup>
</Project>
