﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ISTPortal.Models;

public partial class StoreBatch
{
    public int StoreBatchId { get; set; }

    public int? IstId { get; set; }

    public string StoreMasterBatchCode { get; set; }

    public string StoreBatchCode { get; set; }

    public string StoreBatchTonumber { get; set; }

    /// <summary>
    /// SRC | CWH | DEST
    /// </summary>
    public string StoreBatchOwnerType { get; set; }

    /// <summary>
    /// BP-1, BP-2 | CWH | BP-5, NEEM
    /// </summary>
    public string StoreBatchOwnerCode { get; set; }

    public DateTime? StoreBatchCreatedDate { get; set; }

    public int? StoreBatchCreatedBy { get; set; }

    public DateTime? StoreBatchModifiedDate { get; set; }

    public int? StoreBatchModifiedBy { get; set; }

    public bool? StoreBatchIsActive { get; set; }

    public string StoreTostatus { get; set; }

    public DateTime? TOCreatedDateAX { get; set; }
    public DateTime? TOShippedDateAX { get; set; }
    public DateTime? TOReceivedDateAX { get; set; }
}