using System.ComponentModel.DataAnnotations;

namespace ISTPortal.DTO
{
    public class MenuDTO
    {
        public int MenuId { get; set; }

        [Required(ErrorMessage = "Menu Title is required")]
        [StringLength(500, ErrorMessage = "Menu Title cannot exceed 500 characters")]
        public string MenuTitle { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "Menu URL cannot exceed 500 characters")]
        public string MenuUrl { get; set; } = string.Empty;

        public int? ParentMenuId { get; set; }

        [Range(1, 999, ErrorMessage = "Sort Order must be between 1 and 999")]
        public int MenuSortOrder { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties for Tree Grid
        public string ParentMenuTitle { get; set; } = string.Empty;
        public List<MenuDTO> Children { get; set; } = new List<MenuDTO>();
        public bool HasChildren => Children.Any();

        // For Tree Grid display
        public string TreeId => MenuId.ToString();
        public string TreeParentId => ParentMenuId?.ToString() ?? "";
        public bool IsSelected { get; set; } = false;
    }
}
