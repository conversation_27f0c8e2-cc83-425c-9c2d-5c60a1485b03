﻿namespace ISTPortal.DTO
{
    public class DestinationDTO
    {
        public int destBatchId { get; set; }
        public string istCode { get; set; }
        public string SourceTONumber { get; set; }
        public string SourceTOStatus { get; set; }
        public string CWHTONumber { get; set; }
        public string CWHTOStatus { get; set; }
        public int istTotalItems { get; set; } = 0;
        public int istTotalQty { get; set; } = 0;
        public int SrcTotalItems { get; set; } = 0;
        public int SrcTotalQty { get; set; } = 0;
        public int CWHTotalItems { get; set; } = 0;
        public int CWHTotalQty { get; set; } = 0;
        public string FromStore { get; set; }
        public string ToStore { get; set; }
        public string destToStatus { get; set; } = "";
    }
}
