using ISTPortal.Data;
using ISTPortal.DTO;
using Microsoft.EntityFrameworkCore;

namespace ISTPortal.Services
{
    public class UserRightsService
    {
        private readonly ApplicationDbContext _context;

        public UserRightsService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<StoreDTO>> GetAvailableStoresAsync()
        {
            return await _context.Stores
                .Where(s => s.IsActive)
                .OrderBy(s => s.Name)
                .Select(s => new StoreDTO
                {
                    Id = s.Id,
                    Code = s.Code,
                    Name = s.Name,
                    IsActive = s.IsActive
                })
                .ToListAsync();
        }

        public async Task<List<MenuDTO>> GetAvailableMenusForTreeAsync()
        {
            return await _context.Menus
                .Where(m => m.IsActive)
                .OrderBy(m => m.MenuSortOrder)
                .Select(m => new MenuDTO
                {
                    MenuId = m.MenuId,
                    MenuTitle = m.MenuTitle,
                    MenuUrl = m.MenuUrl ?? "",
                    ParentMenuId = m.ParentMenuId,
                    MenuSortOrder = m.MenuSortOrder,
                    IsActive = m.IsActive
                })
                .ToListAsync();
        }

        public Task<List<StoreDTO>> GetUserStoresAsync(int userId)
        {
            var stores = (from s in _context.Stores
                          orderby s.Name
                          where s.IsActive
                         select new StoreDTO
                         {
                             Id = s.Id,
                             Code = s.Code,
                             Name = s.Name,
                             Manager = s.Manager,
                             IsSelected = false
                             //Id = us.Id,
                             //UserId = us.UserId ?? 0,
                             //StoreId = us.StoreId ?? 0,
                             //UserName = u.FullName,
                             //StoreName = s.Name,
                             //StoreCode = s.Code,
                             //IsSelected = true
                         }).ToList();
            // get user's assigned stores
            var assignedStoreIds = _context.UserStores
                .Where(us => us.UserId == userId)
                .Select(us => us.StoreId ?? 0)
                .ToList();
            // mark stores as selected based on assigned stores
            foreach (var store in stores)
            {
                store.IsSelected = assignedStoreIds.Contains(store.Id);
            }
            return Task.FromResult(stores);

        }

        public Task<List<MenuDTO>> GetUserMenusAsync(int userId)
        {
            var userMenus = (from a in _context.UserMenus
                           where a.UserId == userId
                           select a.MenuId).ToList();

            var mnus = (from m in _context.Menus
                        orderby m.ParentMenuId, m.MenuTitle
                        select new MenuDTO
                        {
                            MenuId = m.MenuId,
                            MenuTitle = m.MenuTitle,
                            MenuUrl = m.MenuUrl ?? "",
                            ParentMenuId = m.ParentMenuId,
                            IsSelected = userMenus.Contains(m.MenuId)
                        }).ToList();

            return Task.FromResult(mnus);
            // get user's assigned menus
            
        }

        public async Task<List<int>> GetUserAssignedStoreIdsAsync(int userId)
        {
            return await _context.UserStores
                .Where(us => us.UserId == userId)
                .Select(us => us.StoreId ?? 0)
                .ToListAsync();
        }

        public async Task<List<int>> GetUserAssignedMenuIdsAsync(int userId)
        {
            return await _context.UserMenus
                .Where(um => um.UserId == userId)
                .Select(um => um.MenuId)
                .ToListAsync();
        }
    }
}
