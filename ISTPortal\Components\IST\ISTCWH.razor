﻿@page "/planning/istcwh"
@using ISTPortal.DTO
@using ISTPortal.Services
@using Syncfusion.Blazor.Cards
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Notifications
@inject Microsoft.FluentUI.AspNetCore.Components.IDialogService DialogService
@inject Syncfusion.Blazor.Popups.SfDialogService sfDialogService
@inject NavigationManager NavigationManager
@inject ISTDataService istService
@inject IJSRuntime JS

@rendermode InteractiveServer


<style>
    .componentWidth {
        width: -webkit-fill-available;
    }

    label {
        padding-left: 2px;
    }

    .gridlbl {
        font-size: 12px;
        font-weight: 500;
    }

    .cardrow {
        border: 1px solid #ccc;
        border-radius: 5px;
        padding: 10px;
        margin: 0px;
        background-color: #036ac4;
        color: white;
    }

    .file-input {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .e-grid td.e-active {
        background: powderblue !important;
    }

    .istMsg {
        font-size: 14px;
        color: firebrick;
        padding-left: 25px;
        padding-top: 5px;
    }

    .tbllbl {
        font-size: small;
        font-weight: 400;
    }

    .closeBoxdiv {
        border: 1px solid orange;
        border-radius: 8px;
        margin: 10px 0px 10px 0px;
        height: 65px;
        font-weight: 500;
        padding: 2px 0px 5px 10px;
        background-color: lightcyan;
    }
</style>


<FluentBreadcrumb>
    <FluentBreadcrumbItem Href="/">
        <FluentIcon Value="@(new Icons.Regular.Size16.Home())" Color="@Color.Neutral" Slot="start" />
    </FluentBreadcrumbItem>
    <FluentBreadcrumbItem Href="/planning/istcwh">
        IST CWH
    </FluentBreadcrumbItem>
</FluentBreadcrumb>

@* <div class="row mt-2">
    <div class="col-2">
        <FluentDatePicker Label="Start Date" AriaLabel="Start Date" Class="componentWidth" @bind-Value="@filter.fromDate" />
    </div>
    <div class="col-2">
        <FluentDatePicker Label="End Date" Class="componentWidth" AriaLabel="End Date" @bind-Value="@filter.toDate" />
    </div>
    <div class="col-md-3">
        <FluentTextField Class="componentWidth" @bind-Value="filter.ISTCode" Label="IST Code"></FluentTextField>
    </div>
</div>

<FluentStack HorizontalGap="15" Class="mt-2 mb-2">
    <FluentButton IconStart="@(new Icons.Regular.Size16.Search())"
    BackgroundColor="#00c853" Color="white">
        Search
    </FluentButton>
</FluentStack> *@

<SfToast @ref="toastObj"></SfToast>

<SfGrid @ref="dgIST"
        DataSource="@istList"
        AllowFiltering="true">
    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
    <GridTemplates>
        <DetailTemplate>
            @{
                if (context is ISTDTO ist)
                {
                    if (ist.istStoreBatch.FirstOrDefault()!.storeBatchCWH! != null)
                    {
                        <table class="table-info table-bordered table-sm tbllbl">
                            <thead>
                                <tr>
                                    <td scope="col" style="width:170px;">TO #</td>
                                    <td scope="col">Status</td>
                                    <td scope="col">Created By</td>
                                    <td scope="col">Created Date</td>
                                    @*   <td scope="col">TO Created</td>
                                    <td scope="col">TO Shipped</td>
                                    <td scope="col">TO Received</td>*@
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in ist.istStoreBatch.FirstOrDefault()!.storeBatchCWH!)
                                {
                                    <tr>
                                        <td scope="col" style="width:170px;">@item.CWHToNumber</td>
                                        <td scope="col">
                                            @if (item.CWHToStatus == "DRAFT")
                                            {
                                                <FluentBadge Appearance="Appearance.Neutral" Fill="highlight" BackgroundColor="#ffd800;" Color="#000">@item.CWHToStatus</FluentBadge>
                                            }
                                            else
                                            {
                                                <FluentBadge Appearance="Appearance.Accent"> @item.CWHToStatus </FluentBadge>
                                            }
                                        </td>
                                        <td scope="col">@item.storeCreatedByStrCWH</td>
                                        @*<td scope="col">
                                            @item.storeCreatedDateCWH!.Value.ToString("dd-MMM-yy HH:mm")
                                        </td>
                                        <td scope="col">
                                            @item.cwhTOCreatedDateAX!.Value.ToString("dd-MMM-yy HH:mm")
                                        </td>
                                        <td scope="col">
                                            @item.cwhTOShippedDateAX!.Value.ToString("dd-MMM-yy HH:mm")
                                        </td>
                                        <td scope="col">
                                            @item.cwhTOReceivedDateAX!.Value.ToString("dd-MMM-yy HH:mm")
                                        </td>*@
                                    </tr>
                                }
                            </tbody>
                        </table>
                    }
                    else
                    {
                        <div class="istMsg">
                            You have accepted it but have not assigned TO Number.
                        </div>
                    }

                }
            }
        </DetailTemplate>
    </GridTemplates>
    <GridColumns>
        <GridColumn Field=@nameof(ISTDTO.IstCode) HeaderText="IST Code" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">@ctx.IstCode</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">@ctx.IstTitle</span>
                        </div>
                    </div>
                    @*<div class="row">
                        <div class="col">
                            --<span class="gridlbl">@ctx.storeBatchId</span>
                        </div>
                    </div>*@
                }
            </Template>
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.TONumber) HeaderText="Source TO #" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">@ctx.TONumber</span><br />
                            <FluentBadge Appearance="Appearance.Accent">@ctx.istStoreBatch.FirstOrDefault()!.storeTOStatus</FluentBadge>
                            @*    <br/>
                            <span class="gridlbl">C: @ctx.istStoreBatch.FirstOrDefault()!.TOCreatedDateAX</span><br />
                            <span class="gridlbl">S: @ctx.istStoreBatch.FirstOrDefault()!.TOShippedDateAX</span><br />
                            <span class="gridlbl">R: @ctx.istStoreBatch.FirstOrDefault()!.TOReceivedDateAX</span><br />
                            *@
                        </div>
                        @*   <div class="col">
                         </div>*@
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="IST Item Qty" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Items : @ctx.TotalItems</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Qty. : @ctx.TotalQty</span>
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Source Item Qty" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Items : @ctx.istStoreBatch.FirstOrDefault()!.TotalItems</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Qty. : @ctx.istStoreBatch.FirstOrDefault()!.TotalQty</span>
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Stores" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Source : @ctx.fromStore</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Destination : @ctx.toStore</span>
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        @*  <GridColumn Field=@nameof(StoreBatchDTO.CWHToNumber) HeaderText="CWH TO #" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">@ctx!.istStoreBatch.FirstOrDefault()!.CWHToNumber</span><br />
                            @if (@ctx.istStoreBatch.FirstOrDefault()!.CWHToStatus != "" && @ctx.istStoreBatch.FirstOrDefault()!.CWHToStatus != null)
                            {
                                <FluentBadge Appearance="Appearance.Accent">@ctx.istStoreBatch.FirstOrDefault()!.CWHToStatus</FluentBadge>
                            }
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>*@

        <GridColumn HeaderText=" " AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <SfButton CssClass="e-small e-info e-flat"
                              IconCss="e-icons e-edit" OnClick="@(() =>viewTONumber(ctx))">
                        View
                    </SfButton>
                    <SfButton CssClass="e-small e-success e-flat" IconCss="e-icons e-user-defined"
                              Disabled="@(ctx.istStoreBatch.FirstOrDefault().storeTOStatus == "CLOSED" ? true : false)"
                              OnClick="@(() =>assignTONoByCWH(ctx))">
                        Assign TO #
                    </SfButton>
                    // <SfButton CssClass="e-small e-warning e-flat" IconCss="e-icons e-hand-gestures"
                    //           Disabled="@(@ctx.istStoreBatch.FirstOrDefault()!.CWHToStatus == "DRAFT" ? false:true)"
                    //           OnClick="@(() =>dispatchTONoByCWH(ctx))">
                    //     Dispatch
                    // </SfButton>
                    // <SfButton Disabled="@(@ctx.istStoreBatch.FirstOrDefault()!.CWHToStatus == "DRAFT" ? false : true )" CssClass="e-small e-danger e-flat" IconCss="e-icons e-delete"
                    //           OnClick="@(() => removeTOByCWH(ctx))">
                    //     Remove
                    // </SfButton>
                }
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="750px" Visible="false" @ref="dlgViewIST">
    <DialogTemplates>
        <Header>TO Number : @newISTStoreBatch.TONumber</Header>
        <Content>
            <div class="row">
                <div class="col-md">
                    <b>IST Code :</b> @newISTStoreBatch.IstCode
                </div>
                <div class="col-md">
                    <b>IST Title :</b> @newISTStoreBatch.IstTitle
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Source Store :</b> @newISTStoreBatch.fromStore
                </div>
                <div class="col-md">
                    <b>Destination Store :</b> @newISTStoreBatch.toStore
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Remarks :</b> @newISTStoreBatch.IstDescription
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <SfGrid @ref="dgISTDetail"
                            DataSource="@newISTStoreBatch.istStoreBatchDetail"
                            AllowFiltering="true" Height="220">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn Field=@nameof(StoreBatchDetailDTO.ItemCode) HeaderText="Item Barcode" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(StoreBatchDetailDTO.ItemQty) HeaderText="Item Quantity" TextAlign="TextAlign.Right" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.ItemDesc) HeaderText="Desc." TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR1) HeaderText="HIR1" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR2) HeaderText="HIR2" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR3) HeaderText="HIR3" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            @*       <GridColumn Field=@nameof(StoreBatchDetailDTO.StoreBatchDetailRemarks) HeaderText="Remarks" AutoFit>
                            </GridColumn> *@
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>

            <FluentStack HorizontalGap="15" Class="mt-2 mb-2">
                @*    <FluentButton IconStart="@(new Icons.Regular.Size16.SaveMultiple())"
                              Appearance="Appearance.Accent" OnClick="acceptTO">
                    Accept
                </FluentButton>
                <FluentButton IconStart="@(new Icons.Regular.Size16.CalendarCancel())"
                              Appearance="Appearance.Accent" OnClick="rejectTO">
                    Reject
                </FluentButton> *@
                <FluentButton IconStart="@(new Icons.Regular.Size16.LockClosed())"
                              Appearance="Appearance.Accent" OnClick="@(() => dlgViewIST.HideAsync())">
                    Close
                </FluentButton>
            </FluentStack>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="750px" Visible="false" @ref="dlgAssignTo">
    <DialogTemplates>
        <Header>Assign TO No.</Header>
        <Content>
            <div class="row">
                <div class="col-md-10">
                    <FluentTextField Placeholder="TO No." Class="componentWidth" @bind-Value="CWhTONumberTemp"></FluentTextField>
                </div>
                <div class="col-md-1">
                    <FluentButton IconStart="@(new Icons.Regular.Size16.ArrowDownload())"
                                  Appearance="Appearance.Accent" OnClick="fetchTOItemsforCWH">
                        Fetch
                    </FluentButton>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Source TO #:</b> @newISTStoreBatch.TONumber
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>IST Code :</b> @newISTStoreBatch.IstCode
                </div>
                <div class="col-md">
                    <b>IST Title :</b> @newISTStoreBatch.IstTitle
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Source Store :</b> @newISTStoreBatch.fromStore
                </div>
                <div class="col-md">
                    <b>Destination Store :</b> @newISTStoreBatch.toStore
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Remarks :</b> @newISTStoreBatch.IstDescription
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <SfGrid @ref="dgISTDetail"
                            DataSource="@newISTStoreBatch.istStoreBatchDetail"
                            AllowFiltering="true" Height="220">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn Field=@nameof(StoreBatchDetailDTO.ItemCode) HeaderText="Item Code" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(StoreBatchDetailDTO.ItemQty) HeaderText="Item Qty." TextAlign="TextAlign.Right" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(StoreBatchDetailDTO.fetchQty) HeaderText="TO Qty." TextAlign="TextAlign.Right" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.ItemDesc) HeaderText="Desc." TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR1) HeaderText="HIR1" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR2) HeaderText="HIR2" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR3) HeaderText="HIR3" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            @*   <GridColumn HeaderText="TO Qty." Width="100">
                                <Template>
                                    @{
                                        var ctx = (context as StoreBatchDetailDTO);
                                        <FluentNumberField Min="0" Step="1" ReadOnly @bind-Value="ctx!.fetchQty" />
                                    }
                                </Template>
                            </GridColumn>
                           <GridColumn HeaderText="TO Remarks" Width="250">
                                <Template>
                                    @{
                                        var ctx = (context as StoreBatchDetailDTO);
                                        <FluentTextField Class="componentWidth" @bind-Value="ctx!.TORemarks"></FluentTextField>
                                    }
                                </Template>
                            </GridColumn> *@
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>
            <FluentStack HorizontalGap="15" Class="mt-2 mb-2">
                @*         <FluentButton IconStart="@(new Icons.Regular.Size16.SaveMultiple())"
                              Appearance="Appearance.Accent" OnClick="@(() => saveTOItemsforCWH("DRAFT"))">
                    Save
                </FluentButton> *@
                <FluentButton IconStart="@(new Icons.Regular.Size16.Send())"
                              Appearance="Appearance.Accent" OnClick="@(() => saveTOItemsforCWH("DISPATCHED"))">
                    Save & Dispatched
                </FluentButton>
                <FluentButton IconStart="@(new Icons.Regular.Size16.LockClosed())"
                              Appearance="Appearance.Accent" OnClick="@(() => dlgAssignTo.HideAsync())">
                    Close
                </FluentButton>
            </FluentStack>

            @if (closeBox)
            {
                <div class="closeBoxdiv">
                    Kindly click any one option to indicate whether you want to keep 'IST' partially or close it completely.
                    <FluentButton Class="mt-1" IconStart="@(new Icons.Regular.Size16.Send())"
                                  Appearance="Appearance.Accent" OnClick="@(() => closeStoreSrcStatus("P"))">
                        Partially
                    </FluentButton>
                    <FluentButton Class="mt-1" IconStart="@(new Icons.Regular.Size16.SendPerson())"
                                  Appearance="Appearance.Accent" OnClick="@(() => closeStoreSrcStatus("F"))">
                        Fully Closed
                    </FluentButton>
                    <FluentButton Class="mt-1" IconStart="@(new Icons.Regular.Size16.LockClosed())"
                                  Appearance="Appearance.Accent" OnClick="@(() => closeBox = false)">
                        Cancel
                    </FluentButton>
                </div>
            }

        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    private bool closeBox = false;
    private filterForm filter = new();
    private SfToast? toastObj;
    private string CWhTONumberTemp = "";
    private ISTDTO newISTStoreBatch = new();
    private string loginId = "admin";
    private List<ISTDTO> istList = new();
    private SfGrid<ISTDTO>? dgIST;
    private SfGrid<StoreBatchDetailDTO>? dgISTDetail;
    private SfDialog dlgAssignTo, dlgViewIST;

    private async void LoadISTCWH()
    {
        istList = await istService.getISTForCWH(loginId, filter);
        StateHasChanged();
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            LoadISTCWH();
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message.ToString());
        }
    }

    private async void viewTONumber(ISTDTO pIstMaster)
    {
        try
        {
            newISTStoreBatch = new();
            newISTStoreBatch = pIstMaster;
            newISTStoreBatch.istStoreBatchDetail = await istService.getStoreItems(pIstMaster.storeBatchId);
            await dlgViewIST.ShowAsync();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }
    private async void assignTONoByCWH(ISTDTO pIstMaster)
    {
        try
        {
            CWhTONumberTemp = "";
            closeBox = false;
            newISTStoreBatch = new();
            newISTStoreBatch = pIstMaster;
            newISTStoreBatch.istStoreBatchDetail = await istService.getStoreItems(pIstMaster.storeBatchId);
            await dlgAssignTo.ShowAsync();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }
    private async void dispatchTONoByCWH(ISTDTO pIstMaster)
    {
        try
        {
            var result = await sfDialogService.ConfirmAsync("Are you sure you want to dispatched TO Number?", "Confirmation");
            if (result)
            {
                await istService.dispatchTOCWH(pIstMaster, loginId);
                LoadISTCWH();
                await dlgViewIST.HideAsync();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void acceptTO()
    {
        try
        {
            var result = await sfDialogService.ConfirmAsync("Are you sure you want to accept TO Number?", "Confirmation");
            if (result)
            {
                await istService.acceptTOCWH(newISTStoreBatch.storeBatchId, loginId);
                LoadISTCWH();
                await dlgViewIST.HideAsync();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }
    private async void rejectTO()
    {
        try
        {
            var result = await sfDialogService.ConfirmAsync("Are you sure you want to reject TO Number?", "Confirmation");
            if (result)
            {
                await istService.rejectTOCWH(newISTStoreBatch.storeBatchId, loginId);
                LoadISTCWH();
                await dlgViewIST.HideAsync();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }
    private async void fetchTOItemsforCWH()
    {
        try
        {
            //String.IsNullOrEmpty(newISTStoreBatch.istStoreBatch.FirstOrDefault()!.StoreBatchTonumber)
            if (String.IsNullOrEmpty(CWhTONumberTemp))
            {
                await sfDialogService.AlertAsync("TO Number is required.", "Info");
                return;
            }

            string msg = await istService.checkTONoIsExist(CWhTONumberTemp);
            if (msg != "OK")
            {
                await sfDialogService.AlertAsync(msg, "Info");
                return;
            }

            newISTStoreBatch.istStoreBatchDetail = await istService.getStoreItems(newISTStoreBatch.storeBatchId);
            List<StoreBatchDetailDTO> tempStoreBatchDet = await istService.fetchTOItemsforCWH(CWhTONumberTemp, newISTStoreBatch.istStoreBatchDetail);
            newISTStoreBatch.istStoreBatchDetail = new(tempStoreBatchDet);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Info");
        }
    }

    private async void ShowToast(string title, string message, string type)
    {
        if (toastObj == null) return;

        var toast = new ToastModel
            {
                Title = title,
                Content = message,
                CssClass = type == "error" ? "e-toast-danger" : "e-toast-info",
                ShowCloseButton = true,
                Timeout = 5000
            };

        await toastObj.ShowAsync(toast);
    }

    private async void closeStoreSrcStatus(string actionStatus)
    {
        try
        {
            string clickedButtonText = "";
            if (actionStatus == "P")
            {
                clickedButtonText = "Partially";
            }
            else
            {
                clickedButtonText = "Fully Closed";
            }
            var result = await sfDialogService.ConfirmAsync($"You've clicked on a {clickedButtonText}. Do you want to continue?", "Confirmation");
            if (result)
            {
                await istService.saveTOItemsforCWH(newISTStoreBatch, "CWH", "DISPATCHED", loginId, CWhTONumberTemp, actionStatus);
                LoadISTCWH();
                await dlgAssignTo.HideAsync();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void saveTOItemsforCWH(string toStatusCWH)
    {
        try
        {
            if (newISTStoreBatch != null)
            {
                if (String.IsNullOrEmpty(CWhTONumberTemp))
                {
                    await sfDialogService.AlertAsync("TO Number is required.", "Info");
                    return;
                }

                if (!newISTStoreBatch.istStoreBatchDetail.Any())
                {
                    await sfDialogService.AlertAsync("TO Items is required.", "Info");
                    return;
                }

                bool isISTAvailable = await istService.getISTStatus(newISTStoreBatch.IstId ?? 0);
                if (isISTAvailable == false)
                {
                    await sfDialogService.AlertAsync("IST is already closed.", "Info");
                    return;
                }

                closeBox = true;
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void removeTOByCWH(ISTDTO pIstMaster)
    {
        try
        {
            var result = await sfDialogService.ConfirmAsync("Are you sure you want to remove TO#,?", "Confirmation");
            if (result)
            {
                await istService.removeTONoByCWH(pIstMaster, loginId, CWhTONumberTemp);
                LoadISTCWH();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");

        }
    }

}
