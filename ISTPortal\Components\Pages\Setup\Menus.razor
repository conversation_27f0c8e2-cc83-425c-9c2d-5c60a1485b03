﻿@page "/setup/menus"
@using ISTPortal.DTO
@using ISTPortal.Services
@using Syncfusion.Blazor.TreeGrid
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@inject ISTPortal.Services.MenuService MenuService
@inject SfDialogService DialogService
@rendermode InteractiveServer

<PageTitle>Menu Management</PageTitle>

<div class="">
    <div class="row">
        <div class="col-12">
            <div class="card" style="height: calc(100vh - 75px); overflow:hidden">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">Menu Management</h4>
                    <SfButton CssClass="e-primary" @onclick="OpenAddDialog">
                        <i class="fas fa-plus"></i> Add Menu
                    </SfButton>
                </div>
                <div class="card-body">
                    <SfTreeGrid DataSource="@menus" IdMapping="MenuId" ParentIdMapping="ParentMenuId"
                               TreeColumnIndex="0" AllowPaging="true" AllowSorting="true"
                               AllowFiltering="true" Height="calc(100vh - 200px)">
                        <TreeGridPageSettings PageSize="20"></TreeGridPageSettings>
                        <TreeGridFilterSettings Type="Syncfusion.Blazor.TreeGrid.FilterType.Excel"></TreeGridFilterSettings>
                        <TreeGridColumns>
                            <TreeGridColumn Field="@nameof(MenuDTO.MenuTitle)" HeaderText="Menu Title" Width="250"></TreeGridColumn>
                            <TreeGridColumn Field="@nameof(MenuDTO.MenuUrl)" HeaderText="URL" Width="200"></TreeGridColumn>
                            <TreeGridColumn Field="@nameof(MenuDTO.MenuSortOrder)" HeaderText="Sort Order" Width="120"></TreeGridColumn>
                            <TreeGridColumn Field="@nameof(MenuDTO.IsActive)" HeaderText="Active" Width="80" Type="Syncfusion.Blazor.Grids.ColumnType.Boolean"></TreeGridColumn>
                            <TreeGridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                <Template>
                                    @{
                                        var menu = (context as MenuDTO);
                                    }
                                    <div class="btn-group">
                                        <SfButton CssClass="e-small e-info" @onclick="() => OpenEditDialog(menu)">
                                            Edit
                                        </SfButton>
                                        <SfButton CssClass="e-small e-danger" @onclick="() => DeleteMenu(menu)">
                                            Delete      
                                        </SfButton>
                                    </div>
                                </Template>
                            </TreeGridColumn>
                        </TreeGridColumns>
                    </SfTreeGrid>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Menu Form Dialog -->
<SfDialog @ref="menuDialog" Header="@dialogTitle" Width="600px" Height="450px"
          IsModal="true" @bind-Visible="isDialogVisible" ShowCloseIcon="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentMenu" OnValidSubmit="@SaveMenu">
                <DataAnnotationsValidator />
                <div class="row">
                    <div class="col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Menu Title <span class="text-danger">*</span></label>
                            <SfTextBox @bind-Value="currentMenu.MenuTitle" Placeholder="Enter menu title"></SfTextBox>
                            <ValidationMessage For="@(() => currentMenu.MenuTitle)" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Menu URL</label>
                            <SfTextBox @bind-Value="currentMenu.MenuUrl" Placeholder="Enter menu URL (e.g., /page/url)"></SfTextBox>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Parent Menu</label>
                            <SfDropDownList TValue="int?" TItem="MenuDTO" @bind-Value="currentMenu.ParentMenuId"
                                          DataSource="@parentMenus" Placeholder="Select parent menu">
                                <DropDownListFieldSettings Value="MenuId" Text="MenuTitle"></DropDownListFieldSettings>
                            </SfDropDownList>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Sort Order</label>
                            <SfNumericTextBox @bind-Value="currentMenu.MenuSortOrder" Min="1" Max="999"
                                            Placeholder="Enter sort order"></SfNumericTextBox>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <SfCheckBox @bind-Checked="currentMenu.IsActive" Label="Active"></SfCheckBox>
                        </div>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
    <DialogButtons>
        <DialogButton Content="Save" IsPrimary="true" OnClick="@SaveMenu" />
        <DialogButton Content="Cancel" OnClick="@CloseDialog" />
    </DialogButtons>
</SfDialog>

@code {
    private List<MenuDTO> menus = new();
    private List<MenuDTO> parentMenus = new();
    private MenuDTO currentMenu = new();
    private SfDialog menuDialog = new();
    private bool isDialogVisible = false;
    private string dialogTitle = "";
    private bool isEditMode = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadMenus();
        await LoadParentMenus();
    }

    private async Task LoadMenus()
    {
        menus = await MenuService.GetMenusForTreeGridAsync();
        StateHasChanged();
    }

    private async Task LoadParentMenus()
    {
        parentMenus = await MenuService.GetParentMenusAsync();
        StateHasChanged();
    }

    private async Task OpenAddDialog()
    {
        currentMenu = new MenuDTO
        {
            IsActive = true,
            MenuSortOrder = await MenuService.GetNextSortOrderAsync()
        };
        dialogTitle = "Add New Menu";
        isEditMode = false;
        isDialogVisible = true;
    }

    private void OpenEditDialog(MenuDTO menu)
    {
        if (menu == null) return;

        currentMenu = new MenuDTO
        {
            MenuId = menu.MenuId,
            MenuTitle = menu.MenuTitle,
            MenuUrl = menu.MenuUrl,
            ParentMenuId = menu.ParentMenuId,
            MenuSortOrder = menu.MenuSortOrder,
            IsActive = menu.IsActive
        };
        dialogTitle = "Edit Menu";
        isEditMode = true;
        isDialogVisible = true;
    }

    private async Task SaveMenu()
    {
        try
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(currentMenu.MenuTitle))
            {
                await DialogService.AlertAsync("Menu title is required.", "Validation Error");
                return;
            }

            // Check for duplicates
            if (!isEditMode)
            {
                if (await MenuService.MenuTitleExistsAsync(currentMenu.MenuTitle))
                {
                    await DialogService.AlertAsync("Menu title already exists.", "Validation Error");
                    return;
                }
            }
            else
            {
                if (await MenuService.MenuTitleExistsAsync(currentMenu.MenuTitle, currentMenu.MenuId))
                {
                    await DialogService.AlertAsync("Menu title already exists.", "Validation Error");
                    return;
                }
            }

            bool success;
            if (isEditMode)
            {
                success = await MenuService.UpdateMenuAsync(currentMenu, "admin"); // TODO: Get current user
            }
            else
            {
                success = await MenuService.CreateMenuAsync(currentMenu, "admin"); // TODO: Get current user
            }

            if (success)
            {
                //await DialogService.AlertAsync($"Menu {(isEditMode ? "updated" : "created")} successfully.", "Success");
                CloseDialog();
                await LoadMenus();
                await LoadParentMenus();
            }
            else
            {
                await DialogService.AlertAsync($"Failed to {(isEditMode ? "update" : "create")} menu.", "Error");
            }
        }
        catch (Exception ex)
        {
            await DialogService.AlertAsync($"An error occurred: {ex.Message}", "Error");
        }
    }

    private async Task DeleteMenu(MenuDTO menu)
    {
        if (menu == null) return;

        var confirmed = await DialogService.ConfirmAsync($"Are you sure you want to delete menu '{menu.MenuTitle}'?", "Confirm Delete");
        if (confirmed)
        {
            var success = await MenuService.DeleteMenuAsync(menu.MenuId, "admin"); // TODO: Get current user
            if (success)
            {
                await DialogService.AlertAsync("Menu deleted successfully.", "Success");
                await LoadMenus();
                await LoadParentMenus();
            }
            else
            {
                await DialogService.AlertAsync("Failed to delete menu. It may have child menus.", "Error");
            }
        }
    }

    private void CloseDialog()
    {
        isDialogVisible = false;
        currentMenu = new();
    }
}
