using System.ComponentModel.DataAnnotations;

namespace ISTPortal.DTO
{
    public class UserDTO
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "User ID is required")]
        [StringLength(50, ErrorMessage = "User ID cannot exceed 50 characters")]
        public string UserId { get; set; } = string.Empty;

        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "Full Name is required")]
        [StringLength(100, ErrorMessage = "Full Name cannot exceed 100 characters")]
        public string FullName { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        // Navigation properties for UI
        public List<int> AssignedStoreIds { get; set; } = new List<int>();
        public List<int> AssignedMenuIds { get; set; } = new List<int>();
        public List<UserStoreDTO> AssignedStores { get; set; } = new List<UserStoreDTO>();
        public List<MenuDTO> AssignedMenus { get; set; } = new List<MenuDTO>();
        public string Status=> IsActive ? "Active" : "Inactive";
    }
}
