﻿namespace ISTPortal.DTO;
using System;
public class IstTimeComplianceDto
{
    // IST basic information
    public int IstID { get; set; }
    public string IstCode { get; set; }
    public string IstTitle { get; set; }
    public DateTime IstCreatedDate { get; set; }
    public string IstPriority { get; set; }

    // Store information
    public string FromStoreCode { get; set; }
    public string FromStoreName { get; set; }
    public string ToStoreCode { get; set; }
    public string ToStoreName { get; set; }

    // Progress dates and statuses
    public DateTime? DratDate { get; set; }
    public string Draft { get; set; }
    public DateTime? SentDate { get; set; }
    public string Sent { get; set; }
    public DateTime? CencelledDate { get; set; }
    public string Cancelled { get; set; }
    public DateTime? ClosedDate { get; set; }
    public string Closed { get; set; }

    // Date differences
    public int? SentToCancel { get; set; }
    public int? SentToClosed { get; set; }

    // Off dates count
    public int CountOffDates { get; set; }

    // IST allowed days configuration
    public int? IstAllowedDaysID { get; set; }
    public string? IstAllowedDaysPriority { get; set; }
    public int? IstAllowedDaysValue { get; set; }
    public string IstAllowedDaysRemarks { get; set; }
    public DateTime? IstAllowedDaysCreatedDate { get; set; }
    public string IstAllowedDaysCreatedBy { get; set; }

    // Time compliance result
    public string TimeCompliance { get; set; }
}