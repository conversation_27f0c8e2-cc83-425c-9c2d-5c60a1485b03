﻿namespace ISTPortal.DTO
{
    public class StoreBatchDetailDTO
    {
        public int StoreBatchDetailId { get; set; }

        public int? StoreBatchId { get; set; }

        public int? ItemId { get; set; }

        public string ItemCode { get; set; }

        public string ItemMasterCode { get; set; }

        public string ItemName { get; set; }

        public int? ItemQty { get; set; }

        public string StoreBatchDetailRemarks { get; set; }

        public DateTime? StoreBatchDetailCreatedDate { get; set; }

        public int? StoreBatchDetailCreatedBy { get; set; }

        public DateTime? StoreBatchDetailModifiedDate { get; set; }

        public int? StoreBatchDetailModifiedBy { get; set; }

        public bool? StoreBatchDetailIsActive { get; set; }
        public int? fetchQty { get; set; } = 0;
        public string? TORemarks { get; set; } = "";
        public string? HIR1 { get; set; } = "";
        public string? HIR2 { get; set; } = "";
        public string? HIR3 { get; set; } = "";
        public string? ItemDesc { get; set; } = "";
    }
}
