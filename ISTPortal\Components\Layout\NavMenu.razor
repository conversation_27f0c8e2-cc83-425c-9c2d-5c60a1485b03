﻿<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon" />
    <label for="navmenu-toggle" class="navmenu-icon"><FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Fill" /></label>
    <nav class="sitenav" aria-labelledby="main-menu">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="expanded" CustomToggle="true">
            <AuthorizeView>
                <Authorized>
                    <FluentNavLink Href="/" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Home())" IconColor="Color.Accent">Home</FluentNavLink>
                    <FluentNavLink Href="/planning/istmaster" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">IST Planning</FluentNavLink>
                    <FluentNavLink Href="/planning/istsource" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">Sending Store</FluentNavLink>
                    <FluentNavLink Href="/planning/istcwh" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">CWH</FluentNavLink>
                    <FluentNavLink Href="/planning/istdest" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">Receiving Store</FluentNavLink>

                    <FluentNavGroup Expanded="false" Title="Setup"
                                    Icon="@(new Icons.Filled.Size16.ContentView())">
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/setup/users">Users</FluentNavLink>
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/setup/menus">Menus</FluentNavLink>
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/setup/stores">Stores</FluentNavLink>
                    </FluentNavGroup>

                    <FluentNavGroup Expanded="false" Title="Reports"
                                    Icon="@(new Icons.Filled.Size16.ContentView())">
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/reports/rptistinfo">IST Info</FluentNavLink>
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/reports/rptisttimecompliance">IST Time Compliance</FluentNavLink>
                    </FluentNavGroup>
                    <FluentNavLink Href="/logout" Icon="@(new Icons.Regular.Size20.SignOut())" IconColor="Color.Accent">Logout</FluentNavLink>
                </Authorized>
                <NotAuthorized>
                    <div class="container">
                        <h1>Sorry</h1>
                        <p>You're not authorized to reach this page.</p>
                        <p>You may need to log in as a different user.</p>
                        <a href="/login">Log in</a>
                    </div>
                </NotAuthorized>
            </AuthorizeView>
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;
}
