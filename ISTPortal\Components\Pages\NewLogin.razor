﻿@page "/login"
@using System.Security.Claims
@using Microsoft.AspNetCore.Antiforgery
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies
@inject ISTPortal.Services.UserService UserService
@layout Layout.BlankLayout
@inject NavigationManager Navigation
@inject ISTPortal.Services.AuthenticationService AuthenticationService
@attribute [RequireAntiforgeryToken]

@attribute [ExcludeFromInteractiveRouting]


<style>
    body {
        margin: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #e6f0fa, #f0f7fd);
        background-attachment: fixed;
    }

    .login-container {
        display: flex;
        height: 100vh;
        justify-content: center;
        align-items: center;
    }

    .login-panel {
        background-color: white;
        padding: 2.5rem 2rem;
        border-radius: 14px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
        width: 360px;
        animation: fadeIn 0.5s ease-in-out;
    }

    .login-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 2rem;
    }

    .logo-img {
        width: 48px;
        height: 48px;
        margin-right: 0.75rem;
    }

    .portal-title {
        font-size: 1.6rem;
        color: #036ac4;
        font-weight: 600;
        margin: 0;
    }

    .form-group {
        text-align: left;
        margin-bottom: 1.2rem;
    }

    label {
        display: block;
        font-weight: 600;
        margin-bottom: 0.4rem;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 0.6rem;
        border: 1px solid #ccc;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

        .form-control:focus {
            border-color: #036ac4;
            outline: none;
        }

    .btn-login {
        width: 100%;
        background-color: #036ac4;
        color: white;
        border: none;
        padding: 0.75rem;
        font-size: 1rem;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s ease;
        margin-top: 1rem;
    }

        .btn-login:hover {
            background-color: #0256a1;
        }

    fadeIn {
        from

    {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }

    }
</style>

@* <EditForm Model="loginModel" OnValidSubmit="HandleLogin" FormName="LoginForm">
    <DataAnnotationsValidator /> *@
<form action="" onsubmit="@HandleLogin" method="post" @formname="main">
    <div class="login-container">
        <div class="login-panel">
            <div class="login-header">
                <img src="/images/wh-logo.png" alt="Logo" class="logo-img" />
                <h1 class="portal-title">IST PORTAL</h1>
            </div>

            <div class="form-group">
                <label for="userId">User ID</label>
                <InputText id="userId" @bind-Value="loginModel.UserId" class="form-control" />
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <InputText id="password" @bind-Value="loginModel.Password" class="form-control" type="password" />
            </div>
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div style="color: #d32f2f; text-align: center; margin-top: 1rem; font-weight: 500;">@errorMessage</div>
            }
            <button type="submit" class="btn-login">Login</button>
        </div>
    </div>
    <AntiforgeryToken />
</form>
@* </EditForm> *@

@code {
    protected override async Task OnInitializedAsync()
    {
        await AuthenticationService.ClearAuthenticationAsync();
    }
    [SupplyParameterFromForm] public LoginModel loginModel { get; set; } = new();
    [CascadingParameter] public HttpContext? HttpContext { get; set; }
    private string errorMessage;
    private bool isLoading = false;

    private async Task HandleLogin()
    {

        errorMessage = string.Empty;
        isLoading = true;
        StateHasChanged();

        var user = await UserService.AuthenticateUserAsync(loginModel.UserId, loginModel.Password);
        if (user != null)
        {
            // Persist authentication state
            await AuthenticationService.SetAuthenticationAsync(new ISTPortal.Models.User
                {
                    Id = user.Id,
                    UserId = user.UserId,
                    FullName = user.FullName,
                    IsActive = user.IsActive
                });

            var claims = new List<Claim>
    {
                new Claim(ClaimTypes.Name, user.UserId),
                //new Claim(ClaimTypes.Role, res.Item3.ToString()),
                //new Claim("Id", uu.ToString()),

            };
            var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
            var prn = new ClaimsPrincipal(identity);
            if (HttpContext != null) await HttpContext.SignInAsync(prn);
            Navigation.NavigateTo("/", true);
        }
        else
        {
            errorMessage = "Invalid User ID or Password.";
        }
        isLoading = false;
    }

    public class LoginModel
    {
        [System.ComponentModel.DataAnnotations.Required(ErrorMessage = "User ID is required")]
        public string UserId { get; set; }
        [System.ComponentModel.DataAnnotations.Required(ErrorMessage = "Password is required")]
        public string Password { get; set; }
    }
}
