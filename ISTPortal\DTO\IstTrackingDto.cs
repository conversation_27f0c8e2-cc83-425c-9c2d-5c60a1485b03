namespace ISTPortal.DTO;
using System;

public class IstTrackingDto
{
    public string IstCode { get; set; }

    public string IstTitle { get; set; }

    public string FromStoreName { get; set; }

    public string ToStoreName { get; set; }

    public string IstPriority { get; set; }

    public string IstStatus { get; set; }

    public DateTime? IstDraftDate { get; set; }

    public DateTime? IstSentDate { get; set; }

    public DateTime? IstStatusDate { get; set; }

    public string IstItemCode { get; set; }

    public decimal? IstItemQty { get; set; }

    public string SendToNo { get; set; }

    public string SendItemCode { get; set; }

    public decimal? SendItemQty { get; set; }

    public decimal? SendItemQtyAx { get; set; }

    public string SendToStatus { get; set; }

    public DateTime? SendToCreatedAx { get; set; }

    public DateTime? SendToShipAx { get; set; }

    public DateTime? SendToRecvAx { get; set; }

    public string CwhToNo { get; set; }

    public string CwhItemCode { get; set; }

    public decimal? CwhItemQty { get; set; }

    public decimal? CwhItemQtyAx { get; set; }

    public string CwhToStatus { get; set; }

    public DateTime? CwhToCreatedAx { get; set; }

    public DateTime? CwhToShipAx { get; set; }

    public DateTime? CwhToRecvAx { get; set; }
}