﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ISTPortal.Models;

public partial class StoreBatchDetail
{
    public int StoreBatchDetailId { get; set; }

    public int? StoreBatchId { get; set; }

    public int? ItemId { get; set; }

    public string ItemCode { get; set; }

    public string ItemMasterCode { get; set; }

    public string ItemName { get; set; }

    public int? ItemQty { get; set; }

    public string StoreBatchDetailRemarks { get; set; }

    public DateTime? StoreBatchDetailCreatedDate { get; set; }

    public int? StoreBatchDetailCreatedBy { get; set; }

    public DateTime? StoreBatchDetailModifiedDate { get; set; }

    public int? StoreBatchDetailModifiedBy { get; set; }

    public bool? StoreBatchDetailIsActive { get; set; }
    public int storeItemOnHandQtyAX { get; set; } = 0;
}