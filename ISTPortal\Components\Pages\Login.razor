@page "/login"
@using ISTPortal.Data
@using ISTPortal.Models
@using ISTPortal.Components.Authentication
@inject NavigationManager NavigationManager
@inject ApplicationDbContext DbContext
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>Login</PageTitle>

<div class="login-container">
    <FluentCard>
        <div class="login-form">
            <h2>Login</h2>
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <FluentMessageBar >@errorMessage</FluentMessageBar>
            }
            <div class="form-group">
                <FluentTextField @bind-Value="userId" Placeholder="User ID" />
            </div>
            <div class="form-group">
                <FluentTextField @bind-Value="password" Type="password" Placeholder="Password" />
            </div>
            <FluentButton Appearance="Appearance.Accent" OnClick="HandleLogin">Login</FluentButton>
        </div>
    </FluentCard>
</div>

@code {
    private string userId = "";
    private string password = "";
    private string errorMessage = "";

    private async Task HandleLogin()
    {
        var user = await DbContext.Users
            .FirstOrDefaultAsync(u => u.UserId == userId && u.Password == password && u.IsActive);

        if (user != null)
        {
            var customAuthStateProvider = (CustomAuthenticationStateProvider)AuthStateProvider;
            customAuthStateProvider.NotifyUserAuthentication(user);
            NavigationManager.NavigateTo("/");
        }
        else
        {
            errorMessage = "Invalid credentials";
        }
    }
}