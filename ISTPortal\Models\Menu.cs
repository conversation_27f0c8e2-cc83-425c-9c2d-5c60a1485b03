﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace ISTPortal.Models;

[Table("Menus")]
public partial class Menu
{
    public int MenuId { get; set; }

    public string MenuTitle { get; set; }

    public string MenuUrl { get; set; }

    public int? ParentMenuId { get; set; }

    public int MenuSortOrder { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public string CreatedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string ModifiedBy { get; set; }

    public virtual ICollection<Menu> InverseParentMenu { get; set; } = new List<Menu>();

    public virtual Menu ParentMenu { get; set; }
    [NotMapped]
    public virtual ICollection<UserMenu> UserMenus { get; set; } = new List<UserMenu>();
}