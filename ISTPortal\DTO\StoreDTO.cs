using System.ComponentModel.DataAnnotations;

namespace ISTPortal.DTO
{
    public class StoreDTO
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Store Code is required")]
        [StringLength(50, ErrorMessage = "Store Code cannot exceed 50 characters")]
        public string Code { get; set; } = string.Empty;

        [Required(ErrorMessage = "Store Name is required")]
        [StringLength(100, ErrorMessage = "Store Name cannot exceed 100 characters")]
        public string Name { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string Address { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        public string Notes { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "Manager name cannot exceed 100 characters")]
        public string Manager { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "Contact Number cannot exceed 50 characters")]
        public string ContactNumber { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;
        public string Status => IsActive ? "Active" : "Inactive";
        public bool IsSelected { get; set; } = false;
    }
}
