﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ISTPortal.Models;

public partial class IstProgress
{
    public int IstProgressId { get; set; }

    public int? IstId { get; set; }

    public int? IstStepId { get; set; }

    public string IstProgressName { get; set; }

    public string IstProgressRemarks { get; set; }

    public DateTime? IstProgressCreatedDate { get; set; }

    public int? IstProgressCreatedBy { get; set; }

    public DateTime? IstProgressModifiedDate { get; set; }

    public int? IstProgressModifiedBy { get; set; }

    public bool? IstProgressIsActive { get; set; }
}