@page "/setup/stores"
@using ISTPortal.DTO
@using ISTPortal.Services
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@inject StoreService StoreService
@inject SfDialogService DialogService
@rendermode InteractiveServer

<PageTitle>Store Management</PageTitle>

<div class="" style="height: 500px">
    <div class="row">
        <div class="col-12">
            <div class="card" style="height: calc(100vh - 75px); overflow:hidden">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">Store Management</h4>
                    <SfButton CssClass="e-primary" @onclick="OpenAddDialog">
                        <i class="fas fa-plus"></i> Add Store
                    </SfButton>
                </div>
                <div class="card-body">
                    <SfGrid DataSource="@stores" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                            AllowResizing="true" Height="calc(100vh - 200px)">
                        
                        <GridPageSettings PageSize="20"></GridPageSettings>
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn Field="@nameof(StoreDTO.Code)" HeaderText="Code" Width="120"></GridColumn>
                            <GridColumn Field="@nameof(StoreDTO.Name)" HeaderText="Name" Width="200"></GridColumn>
                            <GridColumn Field="@nameof(StoreDTO.Manager)" HeaderText="Manager" Width="150"></GridColumn>
                            <GridColumn Field="@nameof(StoreDTO.ContactNumber)" HeaderText="Contact" Width="120"></GridColumn>
                            <GridColumn Field="@nameof(StoreDTO.Address)" HeaderText="Address" Width="200"></GridColumn>
                            <GridColumn Field="@nameof(StoreDTO.Status)" HeaderText="Status" Width="80" ></GridColumn>
                            <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                <Template>
                                    @{
                                        var store = (context as StoreDTO);
                                    }
                                    <div class="btn-group">
                                        <SfButton CssClass="e-small e-info" @onclick="() => OpenEditDialog(store)">
                                            Edit
                                        </SfButton>
                                        <SfButton CssClass="e-small e-danger" @onclick="() => DeleteStore(store)">
                                            Delete
                                        </SfButton>
                                    </div>
                                </Template>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Store Form Dialog -->
<SfDialog @ref="storeDialog" Header="@dialogTitle" Width="600px" 
          IsModal="true" @bind-Visible="isDialogVisible" ShowCloseIcon="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentStore" OnValidSubmit="@SaveStore">
                <DataAnnotationsValidator />
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Code <span class="text-danger">*</span></label>
                            <SfTextBox @bind-Value="currentStore.Code" Placeholder="Enter store code"></SfTextBox>
                            <ValidationMessage For="@(() => currentStore.Code)" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Name <span class="text-danger">*</span></label>
                            <SfTextBox @bind-Value="currentStore.Name" Placeholder="Enter store name"></SfTextBox>
                            <ValidationMessage For="@(() => currentStore.Name)" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Manager</label>
                            <SfTextBox @bind-Value="currentStore.Manager" Placeholder="Enter manager name"></SfTextBox>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Contact Number</label>
                            <SfTextBox @bind-Value="currentStore.ContactNumber" Placeholder="Enter contact number"></SfTextBox>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Address</label>
                            <SfTextBox @bind-Value="currentStore.Address" Placeholder="Enter address" Multiline="true"></SfTextBox>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Notes</label>
                            <SfTextBox @bind-Value="currentStore.Notes" Placeholder="Enter notes" Multiline="true"></SfTextBox>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <span>Status:</span><SfCheckBox @bind-Checked="currentStore.IsActive" Label="Active"></SfCheckBox>
                        </div>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
    <DialogButtons>
        <DialogButton Content="Save" IsPrimary="true" OnClick="@SaveStore" />
        <DialogButton Content="Cancel" OnClick="@CloseDialog" />
    </DialogButtons>
</SfDialog>

@code {
    private List<StoreDTO> stores = new();
    private StoreDTO currentStore = new();
    private SfDialog storeDialog = new();
    private bool isDialogVisible = false;
    private string dialogTitle = "";
    private bool isEditMode = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadStores();
    }

    private async Task LoadStores()
    {
        stores = await StoreService.GetAllStoresAsync();
        StateHasChanged();
    }

    private void OpenAddDialog()
    {
        currentStore = new StoreDTO { IsActive = true };
        dialogTitle = "Add New Store";
        isEditMode = false;
        isDialogVisible = true;
    }

    private void OpenEditDialog(StoreDTO store)
    {
        currentStore = new StoreDTO
        {
            Id = store.Id,
            Code = store.Code,
            Name = store.Name,
            Address = store.Address,
            Notes = store.Notes,
            Manager = store.Manager,
            ContactNumber = store.ContactNumber,
            IsActive = store.IsActive
        };
        dialogTitle = "Edit Store";
        isEditMode = true;
        isDialogVisible = true;
    }

    private async Task SaveStore()
    {
        try
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(currentStore.Code))
            {
                await DialogService.AlertAsync("Store code is required.", "Validation Error");
                return;
            }

            if (string.IsNullOrWhiteSpace(currentStore.Name))
            {
                await DialogService.AlertAsync("Store name is required.", "Validation Error");
                return;
            }

            // Check for duplicates
            if (!isEditMode)
            {
                if (await StoreService.StoreCodeExistsAsync(currentStore.Code))
                {
                    await DialogService.AlertAsync("Store code already exists.", "Validation Error");
                    return;
                }

                if (await StoreService.StoreNameExistsAsync(currentStore.Name))
                {
                    await DialogService.AlertAsync("Store name already exists.", "Validation Error");
                    return;
                }
            }
            else
            {
                if (await StoreService.StoreCodeExistsAsync(currentStore.Code, currentStore.Id))
                {
                    await DialogService.AlertAsync("Store code already exists.", "Validation Error");
                    return;
                }

                if (await StoreService.StoreNameExistsAsync(currentStore.Name, currentStore.Id))
                {
                    await DialogService.AlertAsync("Store name already exists.", "Validation Error");
                    return;
                }
            }

            bool success;
            if (isEditMode)
            {
                success = await StoreService.UpdateStoreAsync(currentStore, 1); // TODO: Get current user ID
            }
            else
            {
                success = await StoreService.CreateStoreAsync(currentStore, 1); // TODO: Get current user ID
            }

            if (success)
            {
                await DialogService.AlertAsync($"Store {(isEditMode ? "updated" : "created")} successfully.", "Success");
                CloseDialog();
                await LoadStores();
            }
            else
            {
                await DialogService.AlertAsync($"Failed to {(isEditMode ? "update" : "create")} store.", "Error");
            }
        }
        catch (Exception ex)
        {
            await DialogService.AlertAsync($"An error occurred: {ex.Message}", "Error");
        }
    }

    private async Task DeleteStore(StoreDTO store)
    {
        var confirmed = await DialogService.ConfirmAsync($"Are you sure you want to delete store '{store.Name}'?", "Confirm Delete");
        if (confirmed)
        {
            var success = await StoreService.DeleteStoreAsync(store.Id, 1); // TODO: Get current user ID
            if (success)
            {
                await DialogService.AlertAsync("Store deleted successfully.", "Success");
                await LoadStores();
            }
            else
            {
                await DialogService.AlertAsync("Failed to delete store.", "Error");
            }
        }
    }

    private void CloseDialog()
    {
        isDialogVisible = false;
        currentStore = new();
    }
}