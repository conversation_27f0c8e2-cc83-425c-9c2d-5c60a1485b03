﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ISTPortal.Models;

public partial class IstDetail
{
    public int IstDetailId { get; set; }

    public int? IstId { get; set; }

    public int? ItemId { get; set; }

    public string ItemCode { get; set; }

    public string ItemMasterCode { get; set; }

    public string ItemName { get; set; }

    public int? ItemQty { get; set; }

    public DateTime? IstDetailCreatedDate { get; set; }

    public int? IstDetailCreatedBy { get; set; }

    public DateTime? IstDetailModifiedDate { get; set; }

    public int? IstDetailModifyBy { get; set; }

    public bool? IstDetailIsActive { get; set; }
    public int istOnHandQty { get; set; } = 0;

}