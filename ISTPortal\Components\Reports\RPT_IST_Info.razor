﻿@page "/reports/rptistinfo"
@using ISTPortal.DTO
@using ISTPortal.Services
@using Syncfusion.Blazor.Cards
@using Syncfusion.Blazor.Grids
@inject Microsoft.FluentUI.AspNetCore.Components.IDialogService DialogService
@inject Syncfusion.Blazor.Popups.SfDialogService sfDialogService
@inject NavigationManager NavigationManager
@inject ISTDataService istService
@inject IJSRuntime JS

@rendermode InteractiveServer

<style>
    .componentWidth {
    width: -webkit-fill-available;
    }

    label {
    padding-left: 2px;
    }

    .cardrow {
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px;
    margin: 0px;
    background-color: #036ac4;
    color: white;
    }

    .file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    }

    .e-grid td.e-active {
    background: powderblue !important;
    }
</style>

<FluentBreadcrumb>
    <FluentBreadcrumbItem Href="/">
        <FluentIcon Value="@(new Icons.Regular.Size16.Home())" Color="@Color.Neutral" Slot="start" />
    </FluentBreadcrumbItem>
    <FluentBreadcrumbItem Href="/reports/rptistinfo">
        Report - IST Info
    </FluentBreadcrumbItem>
</FluentBreadcrumb>

<div class="row mt-2">
    <div class="col-2">
        <FluentDatePicker Label="Start Date" AriaLabel="Start Date" Class="componentWidth" @bind-Value="@filter.fromDate" />
    </div>
    <div class="col-2">
        <FluentDatePicker Label="End Date" Class="componentWidth" AriaLabel="End Date" @bind-Value="@filter.toDate" />
    </div>
    <div class="col-3">
        <FluentTextField Class="componentWidth" @bind-Value="@filter.ISTCode" Label="IST Code"></FluentTextField>
    </div>
    <div class="col-3">

    </div>
</div>

<FluentStack HorizontalGap="15" Class="mt-2">
    <FluentButton IconStart="@(new Icons.Regular.Size16.ContentView())"
    Appearance="Appearance.Accent" OnClick="viewISTReport">
        View Report
    </FluentButton>
</FluentStack>

<SfGrid @ref="dgISTrpt"
        DataSource="@istReportData"
AllowFiltering="true">
    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn Field=@nameof(ISTReportDTO.istCode) HeaderText="IST Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.istTitle) HeaderText="IST Title" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.istItemCode) HeaderText="IST Item Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.istItemMasterCode) HeaderText="IST Item Master Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.istItemQty) HeaderText="IST Item QTY" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.sendItemCode) HeaderText="Send Item Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.sendItemMasterCode) HeaderText="Send Item Master Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.sendItemQty) HeaderText="Send Item Qty" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.sendItemQtyAX) HeaderText="Send Item Qty AX" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.cwhItemCode) HeaderText="CWH Item Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.cwhItemMasterCode) HeaderText="CWH Item Master Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.cwhItemQty) HeaderText="CWH Item Qty" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.dstItemCode)  HeaderText="Dest. Item Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.dstItemMasterCode)  HeaderText="Dest. Item Master Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.dstItemQty)  HeaderText="Dest. Item Qty" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.ACCURACRY_COMPLIANCE) HeaderText="Accuracy Compliance" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.QTY_COMPLIANCE) HeaderText="Qty Compliance" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTReportDTO.STOCK_COMPLIANCE) HeaderText="Stock Compliance" AutoFit="true">
        </GridColumn>
    </GridColumns>
</SfGrid>

@code {
    private filterForm filter = new();
    private List<ISTReportDTO> istReportData = new();
    private SfGrid<ISTReportDTO>? dgISTrpt;

    private string loginId = "admin";
    protected override async Task OnInitializedAsync()
    {
        try
        {
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message.ToString());
        }
    }

    private async void viewISTReport(){
        try
        {
            if (filter.fromDate == null || filter.toDate == null)
            {
                await sfDialogService.AlertAsync("Info", "Please select both start and end dates.");
                return;
            }
            if (filter.fromDate > filter.toDate)
            {
                await sfDialogService.AlertAsync("Info", "Start date cannot be later than end date.");
                return;
            }
            istReportData = await istService.getISTReport(filter);
            this.StateHasChanged();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync("Error", $"An error occurred while fetching the report: {ex.Message}");
        }
    }

}


@* <FluentSelect Label="Destination Store" Class="componentWidth" @bind-Value="@filter.toStoreCode" TOption="string">
    @if (toStores.Any())
    {
        foreach (var item in toStores)
        {
            <FluentOption Value="@(item.storeCode.ToString())">@item.StoreTitle</FluentOption>
        }
    }
    <FluentOption Value="-1">-----None-----</FluentOption>
</FluentSelect> *@