﻿namespace ISTPortal.DTO
{
    public class CWHBatchDetailDTO
    {

        public int CWHBatchDetailId { get; set; }

        public int? CWHBatchId { get; set; }

        public int? ItemId { get; set; }

        public string ItemCode { get; set; }

        public string ItemMasterCode { get; set; }

        public string ItemName { get; set; }

        public int? ItemQty { get; set; }
        public int? ItemQtyReadOnly { get; set; }

        public string CWHBatchDetailRemarks { get; set; }

        public DateTime? CWHBatchDetailCreatedDate { get; set; }

        public int? CWHBatchDetailCreatedBy { get; set; }

        public DateTime? CWHBatchDetailModifiedDate { get; set; }

        public int? CWHBatchDetailModifiedBy { get; set; }

        public bool? CWHBatchDetailIsActive { get; set; }
        public int? fetchQty { get; set; } = 0;
        public string? TORemarks { get; set; } = "";
        public string? HIR1 { get; set; } = "";
        public string? HIR2 { get; set; } = "";
        public string? HIR3 { get; set; } = "";
        public string? ItemDesc { get; set; } = "";
        public int cwhItemOnHandQtyAX { get; set; } = 0;

    }
}
