﻿namespace ISTPortal.DTO
{
    public class StoreBatchDTO
    {
        public int StoreBatchId { get; set; }
        public int? IstId { get; set; }
        public string StoreMasterBatchCode { get; set; }
        public string StoreBatchCode { get; set; }
        public string StoreBatchTonumber { get; set; }
        public string StoreBatchOwnerType { get; set; }
        public string StoreBatchOwnerCode { get; set; }
        public int? TotalItems { get; set; }
        public int? TotalQty { get; set; }
        public DateTime? StoreBatchCreatedDate { get; set; }
        public int? StoreBatchCreatedBy { get; set; }
        public string? StoreBatchCreatedByString { get; set; }
        public DateTime? StoreBatchModifiedDate { get; set; }
        public int? StoreBatchModifiedBy { get; set; }
        public string? StoreBatchModifiedByString { get; set; }
        public bool? StoreBatchIsActive { get; set; }
        public string storeTOStatus { get; set; } = "DRAFT";

        public DateTime? TOCreatedDateAX { get; set; }
        public DateTime? TOShippedDateAX { get; set; }
        public DateTime? TOReceivedDateAX { get; set; }

        //public int? CWHBatchId { get; set; }
        //public string CWHToNumber { get; set; }
        //public string CWHToStatus { get; set; }
        //public int? CWHTotalItems { get; set; }
        //public int? CWHTotalQty { get; set; }
        //public DateTime? cwhTOCreatedDateAX { get; set; }
        //public DateTime? cwhTOShippedDateAX { get; set; }
        //public DateTime? cwhTOReceivedDateAX { get; set; }
        public string? destToStatus { get; set; } = "";

        public List<StoreBatchCWHDTO>? storeBatchCWH { get; set; }

    }
}
